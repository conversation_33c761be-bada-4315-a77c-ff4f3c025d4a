COLOR_TEXT <- TextColor(103, 255, 255);
COLOR_YELLOW <- TextColor(255, 255, 0);
COLOR_GREY <- TextColor(170, 170, 170);
COLOR_WHITE <- TextColor(255, 255, 255);
COLOR_GREEN <- TextColor(0, 255, 0);
COLOR_RED <- TextColor(255, 0, 0);

local info_target_precache = Entities.CreateByClassname("info_target");
info_target_precache.PrecacheSoundScript("weapons/3d/reloads/autogun_b.wav");
info_target_precache.PrecacheSoundScript("swarm/interface/upgrade.wav");
info_target_precache.PrecacheSoundScript("weapons/3d/welder/welddeny.wav");
info_target_precache.PrecacheSoundScript("weapons/3d/vindicator/fire_grenade.wav");
info_target_precache.PrecacheSoundScript("weapons/3d/turret/icestop.wav");
info_target_precache.PrecacheSoundScript("weapons/3d/meds/kitinject.wav");
info_target_precache.PrecacheSoundScript("explosions/teleport_blink.wav");
info_target_precache.PrecacheSoundScript("explosions/teleport_boom01.wav");
info_target_precache.PrecacheSoundScript("weapons/ar2/ar2_reload_push.wav");
info_target_precache.PrecacheSoundScript("weapons/physcannon/energy_sing_explosion2.wav");
info_target_precache.EmitSound("weapons/3d/reloads/autogun_b.wav");
info_target_precache.EmitSound("swarm/interface/upgrade.wav");
info_target_precache.EmitSound("weapons/3d/welder/welddeny.wav");
info_target_precache.EmitSound("weapons/3d/vindicator/fire_grenade.wav");
info_target_precache.EmitSound("weapons/3d/turret/icestop.wav");
info_target_precache.EmitSound("weapons/3d/meds/kitinject.wav");
info_target_precache.EmitSound("explosions/teleport_blink.wav");
info_target_precache.EmitSound("explosions/teleport_boom01.wav");
info_target_precache.EmitSound("weapons/ar2/ar2_reload_push.wav");
info_target_precache.EmitSound("weapons/physcannon/energy_sing_explosion2.wav");

g_marineNames <- [
	"Sarge",
	"Jaeger",
	"Wildcat",
	"Wolfe",
	"Faith",
	"Bastille",
	"Crash",
	"Vegas"
];
g_offhandItems <- [
	"asw_weapon_mines",
	"asw_weapon_gas_grenades",
	"asw_weapon_medkit",
	"asw_weapon_welder",
	"asw_weapon_flares",
	"asw_weapon_laser_mines",
	"asw_weapon_normal_armor",
	"asw_weapon_buff_grenade",
	"asw_weapon_hornet_barrage",
	"asw_weapon_freeze_grenades",
	"asw_weapon_stim",
	"asw_weapon_tesla_trap",
	"asw_weapon_electrified_armor",
	"asw_weapon_fist",
	"asw_weapon_grenades",
	"asw_weapon_night_vision",
	"asw_weapon_smart_bomb",
	"asw_weapon_blink",
	"asw_weapon_jump_jet"
];
g_alienClassnames <- [
	"asw_drone",
	"asw_buzzer",
	"asw_parasite",
	"asw_shieldbug",
	"asw_drone_jumper",
	"asw_harvester",
	"asw_parasite_defanged",
	"asw_queen",
	"asw_boomer",
	"asw_ranger",
	"asw_mortarbug",
	"asw_drone_uber",
	"npc_antlionguard_normal",
	"npc_antlionguard_cavern"
];
g_largeAlienClassnames <- [
	"asw_shieldbug",
	"asw_harvester",
	"asw_queen",
	"asw_boomer",
	"asw_mortarbug",
	"asw_drone_uber",
	"npc_antlionguard_normal",
	"npc_antlionguard_cavern"
];
g_ghostBlacklist <- [
	"asw_parasite",
	"asw_grub",
	"asw_harvester",
	"asw_parasite_defanged",
	"asw_queen",
	"asw_ranger",
	"asw_shaman"
];
g_bossSummons1 <- [
	"asw_harvester",
	"asw_boomer"
];
g_bossSummons2 <- [
	"asw_shieldbug",
	"asw_shieldbug",
	"npc_antlionguard_normal",
	"npc_antlionguard_cavern"
];
g_eliteBlacklist <- [
	"asw_buzzer",
	"asw_parasite",
	"asw_parasite_defanged"
];
g_eliteTypes1 <- [
	"typeFire",
	"typeLightning",
	"typeEarth"
];
g_eliteTypes2 <- [
	"typeCorruption",
	"typeIncorporeality"
];

g_smallExplodeSfx <- [
	"explosions/hornet01.wav",
	"explosions/hornet02.wav"
];
g_bigExplodeSfx <- [
	"explosions/grenade01.wav",
	"explosions/grenade02.wav"
];
g_teslaSfx <- [
	"weapons/3d/tesla/arcimpact01.wav",
	"weapons/3d/tesla/arcimpact02.wav",
	"weapons/3d/tesla/arcimpact03.wav",
	"weapons/3d/tesla/arcimpact04.wav"
];
g_stompSfx <- [
	"aliens/impacts/shieldbug/stomp01.wav",
	"aliens/impacts/shieldbug/stomp02.wav"
];
g_lightningSfx <- [
	"scapes/2d/random_amb/lightning_strike_01.wav",
	"scapes/2d/random_amb/lightning_strike_02.wav",
	"scapes/2d/random_amb/lightning_strike_03.wav",
	"scapes/2d/random_amb/lightning_strike_04.wav"
];
foreach (sound in g_smallExplodeSfx)
{
	info_target_precache.PrecacheSoundScript(sound);
	info_target_precache.EmitSound(sound);
}
foreach (sound in g_bigExplodeSfx)
{
	info_target_precache.PrecacheSoundScript(sound);
	info_target_precache.EmitSound(sound);
}
foreach (sound in g_teslaSfx)
{
	info_target_precache.PrecacheSoundScript(sound);
	info_target_precache.EmitSound(sound);
}
foreach (sound in g_stompSfx)
{
	info_target_precache.PrecacheSoundScript(sound);
	info_target_precache.EmitSound(sound);
}
foreach (sound in g_lightningSfx)
{
	info_target_precache.PrecacheSoundScript(sound);
	info_target_precache.EmitSound(sound);
}
g_particleLibrary <- [
	"ad_red_smallfire",
	"ad_rfire",
	"heal_effect",
	"heal_recharged",
	"stungrenade_arc_sprites",
	"hunter_projectile_explosion_2g",
	"hunter_projectile_explosion_2f",
	"combine_ball_bounce",
	"melee_ground_pound",
	"stungrenade_core_arcs",
	"mining_laser_beam_cp0_glow",
	"explosion_air_small",
	"asw_env_explosion",
	"explosion_shockwave1",
	"powerup_pickup_generic",
	"thorns_marine_buff_start",
	"freeze_grenade_explosion",
	"electrified_armor_burst",
	"heal_recharged_glow",
	"freeze_grenade_sparkles",
	"lifesteal2",
	"vindicator_grenade_flash",
	"ad_jump_particle_fountain",
	"powerup_freeze_bullets",
	"prifle_grenade_sparks",
	"electric_field_ground",
	"grenade_smoke",
	"electric_weapon_zap_muzzle_off",
	"buffgrenade_pulse",
	"ad_jump_particle",
	"ad_eggs_particle_fountain_red",
	"ad_core_tailsfire",
	"powerup_fire_bullets",
	"powerup_electric_bullets",
	"vortigaunt_hand_glow",
	"melee_stun",
	"ad_eggs_particle_pulse_red",
	"melee_charged"
];

g_activeMarines <- [];
g_playerCount <- 0;
g_inventories <- {};
g_gameRunning <- false;
g_playedMaps <- [];
g_rorTimer <- 0;
g_rorDiff <- Convars.GetFloat( "asw_skill" );
g_rorStage <- 1;
g_rorMarineExp <- 0;
g_rorItemPickups <- {};
g_carnageNumber <- 1;
g_totalKills <- 0;
g_rorItemSpawnCount <- 0;
g_queenObj <- null;
g_keyObj <- null;
g_bossObj <- null;
g_killed <- {};

g_usedButtons <- [];
g_rocketQueue <- {};
g_setDamage <- {};
g_deathMarked <- {};
g_burnExplode <- {};
g_takeExtraDamage <- {};
g_healQueue <- {};
g_inDanger <- {};
g_4killsIn1Sec <- {};
g_4killsIn7Sec <- {};
g_frenzy <- {};
g_ecaCooldown <- {};
g_dangerFireNade <- {};
g_dangerFreeze <- {};
g_dangerHeal <- {};
g_bigKillBuff <- {};
g_friendlyAliens <- [];
g_ghosts <- {};
g_elites <- {};
g_noHealing <- {};
g_queenRemove <- {};
g_queenCount <- 0;
g_queenKills <- 0;
g_bossKey <- null;
g_bossKeyPickup <- [false, Vector(0,0,0)];
g_spawnBoss <- 0;
g_bossSpawned <- false;
g_bossKilled <- false;
g_bossAttackTracker <- {};
g_bossAttackQueue <- {};

g_particle_count_0 <- 0;
::g_particle_amt_0 <- 0;
g_particle_count_1 <- 0;
::g_particle_amt_1 <- 0;

asw_gamerules <- Entities.FindByClassname(null, "asw_gamerules");

function IsInArray(item, array)
{
	for (local i = 0 ; i < array.len() ; i++)
	{
		if (item == array[i])
		{
			return true;
		}
	}
	return false;
}

function DelayCodeExecution( string_code, delay )
{
	DoEntFire( "worldspawn", "RunScriptCode", string_code, delay, null, null );
}

function OnMissionStart()
{
	if (FileToString("challenge_risk_of_rain_resetSwitch") == "true")
	{
		RorReset();
	}
	if (FileToString("challenge_risk_of_rain_playedMaps") != "")
	{
		g_playedMaps = split(FileToString("challenge_risk_of_rain_playedMaps"), "|");
	}
}

function OnGameplayStart()
{
	ClientPrint(null, 2, " ");
	ClientPrint(null, 2, "==========");
	ClientPrint(null, 2, "Currently running \"Risk of Rain\" by ModdedMarionette on AS:RD Steam Workshop.");
	ClientPrint(null, 2, "Version: v5.8");
	ClientPrint(null, 2, "==========");
	ClientPrint(null, 2, "#ror_translationCredit");
	ClientPrint(null, 2, "#ror_translationVersion");
	ClientPrint(null, 2, "==========");
	ClientPrint(null, 2, " ");
	if (IsInArray(GetMapName().tolower(), g_playedMaps))
	{
		Director.RestartMission();
		ClientPrint(null, 3, "#ror_mapOnCooldown");
	}
	else
	{
		RorInit();
		g_gameRunning = true;
		StringToFile("challenge_risk_of_rain_resetSwitch", "true");
		ClientPrint(null, 3, "#ror_howToInv", COLOR_TEXT, COLOR_YELLOW + "/i" + COLOR_TEXT);
		local marine = null;
		while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
		{
			ProcInit(marine);
			local hRorHud = Entities.CreateByClassname( "rd_hud_vscript" );
			hRorHud.__KeyValueFromString( "client_vscript", "challenge_risk_of_rain_hud.nut" );
			hRorHud.Spawn();
			hRorHud.Activate();
			hRorHud.SetEntity( 0, marine );
			hRorHud.SetName( marine.GetName() + "_hud" );
		}
		foreach (name in g_alienClassnames)
		{
			local alien = null;
			while ((alien = Entities.FindByClassname(alien, name)) != null)
			{
				local hpbar = Entities.CreateByClassname( "asw_health_bar" );
				hpbar.__KeyValueFromString("StartDisabled", "0");
				hpbar.__KeyValueFromString("color", "255 0 0 255");
				hpbar.__KeyValueFromString("hideatfullhealth", "1");
				if (alien.GetMaxHealth() <= 104)
				{
					hpbar.__KeyValueFromString("scale", "0.75");
				}
				else
				{
					hpbar.__KeyValueFromString("scale", "1.0");
				}
				hpbar.SetOrigin(alien.GetOrigin()+Vector(0, 0, 50));
				hpbar.SetParent(alien);
				hpbar.SetName("asw_hpbar_ror");
				hpbar.Spawn();
				hpbar.Activate();
				if (alien in g_elites)
				{
					hpbar.__KeyValueFromString("color", "255 0 255 255");
				}
			}
		}
	}
}

function Update()
{
	foreach (victim, attacker in g_killed)
	{
		if (!(victim.IsValid()))
		{
			g_killed.rawdelete(victim);
		}
	}
	foreach (item, value in g_rorItemPickups)
	{
		if (!(item.IsValid()))
		{
			g_rorItemPickups.rawdelete(item);
		}
		else
		{
			if (item.GetMoveParent() == null)
			{
				if ((item.GetOrigin() - g_rorItemPickups[item][1]).Length() > 20)
				{
					item.SetOrigin(g_rorItemPickups[item][1]);
					item.SetVelocity(Vector(0, 0, 0));
				}
				item.SetAnglesVector(item.GetAngles() + Vector(0, 10, 0));
			}
		}
	}
	foreach (item, value in g_rocketQueue)
	{
		if (!(item.IsValid()))
		{
			g_rocketQueue.rawdelete(item);
		}
		else
		{
			if (g_rocketQueue[item].len() > 0)
			{
				local rocket = Entities.CreateByClassname("asw_rocket");
				rocket.SetOrigin(item.GetOrigin() + Vector(0, 0, 50));
				rocket.SetAngles(-45.0, RandomFloat(0.0, 360.0), 0.0);
				rocket.Spawn();
				rocket.Activate();
				rocket.SetName(item.GetMarineName());
				g_setDamage[rocket] <- g_rocketQueue[item][0];
				g_rocketQueue[item].remove(0);
			}
		}
	}
	foreach (item, value in g_setDamage)
	{
		if (!(item.IsValid()))
		{
			g_setDamage.rawdelete(item);
		}
	}
	foreach (item, value in g_deathMarked)
	{
		if (!(item.IsValid()))
		{
			g_deathMarked.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				if (item.GetHealth() > 0)
				{
					SpawnParticle("ad_red_smallfire", item.GetOrigin() + Vector(0, 0, 75), 0.1);
					SpawnParticle("ad_rfire", item.GetOrigin(), 0.1);
				}
				g_deathMarked[item] = g_deathMarked[item] - 1;
			}
			else
			{
				g_deathMarked.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_burnExplode)
	{
		if (!(item.IsValid()) || !(value.IsValid()))
		{
			g_burnExplode.rawdelete(item);
		}
		else if (!(NetProps.GetPropBool(item, "m_bOnFire")))
		{
			g_burnExplode.rawdelete(item);
		}
	}
	foreach (item, value in g_takeExtraDamage)
	{
		if (!(item.IsValid()))
		{
			g_takeExtraDamage.rawdelete(item);
		}
	}
	foreach (item, value in g_healQueue)
	{
		if (!(item.IsValid()) || item.GetHealth() >= item.GetMaxHealth())
		{
			g_healQueue.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				SpawnParticle("heal_effect", item.GetOrigin() + Vector(0, 0, 50), 0.1);
				g_healQueue[item] = g_healQueue[item] - 1;
			}
			else
			{
				if (item.GetHealth() < item.GetMaxHealth())
				{
					Heal(item, 20/(1+0.3*GetMarineLevel()) + item.GetMaxHealth()*0.05*RorCountItem(item.GetMarineName(), "tierWhite", "asw_weapon_stim"));
					item.EmitSound("weapons/3d/reloads/autogun_b.wav");
				}
				g_healQueue.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_inDanger)
	{
		if (!(item.IsValid()))
		{
			g_inDanger.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_inDanger[item] = g_inDanger[item] - 1;
			}
			else
			{
				g_inDanger.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_4killsIn1Sec)
	{
		if (!(item.IsValid()))
		{
			g_4killsIn1Sec.rawdelete(item);
		}
		else
		{
			if (g_4killsIn1Sec[item].len() >= 4)
			{
				if (RorCountItem(item.GetMarineName(), "tierGreen", "asw_weapon_normal_armor") > 0)
				{
					g_frenzy[item] <- 20 + 40*RorCountItem(item.GetMarineName(), "tierGreen", "asw_weapon_normal_armor");
				}
				while (g_4killsIn1Sec[item].len() > 0)
				{
					g_4killsIn1Sec[item].remove(0);
				}
			}
			else
			{
				for (local i = 0 ; i < g_4killsIn1Sec[item].len() ; i++)
				{
					if (g_4killsIn1Sec[item][i] > 0)
					{
						g_4killsIn1Sec[item][i] = g_4killsIn1Sec[item][i] - 1;
					}
					else
					{
						g_4killsIn1Sec[item].remove(i);
					}
				}
			}
		}
	}
	foreach (item, value in g_4killsIn7Sec)
	{
		if (!(item.IsValid()))
		{
			g_4killsIn7Sec.rawdelete(item);
		}
		else
		{
			if (g_4killsIn7Sec[item].len() >= 4)
			{
				if (RorCountItem(item.GetMarineName(), "tierRed", "asw_weapon_grenades") > 0)
				{
					local grenade = DropFragGrenade(1000*RorCountItem(item.GetMarineName(), "tierRed", "asw_weapon_grenades"), 280.0, item.GetOrigin() + Vector(0, 0, 100));
					if (GetNearestAlien(item.GetOrigin(), 1200.0) != null)
					{
						grenade.SetForwardVector(GetNearestAlien(item.GetOrigin(), 1200.0).GetOrigin() - grenade.GetOrigin());
					}
					else
					{
						grenade.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
					}
					grenade.SetVelocity(grenade.GetForwardVector()*1000);
					grenade.SetName(item.GetMarineName());
				}
				while (g_4killsIn7Sec[item].len() > 0)
				{
					g_4killsIn7Sec[item].remove(0);
				}
			}
			else
			{
				for (local i = 0 ; i < g_4killsIn7Sec[item].len() ; i++)
				{
					if (g_4killsIn7Sec[item][i] > 0)
					{
						g_4killsIn7Sec[item][i] = g_4killsIn7Sec[item][i] - 1;
					}
					else
					{
						g_4killsIn7Sec[item].remove(i);
					}
				}
			}
		}
	}
	foreach (item, value in g_frenzy)
	{
		if (!(item.IsValid()))
		{
			g_frenzy.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_frenzy[item] = g_frenzy[item] - 1;
			}
			else
			{
				g_frenzy.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_ecaCooldown)
	{
		if (!(item.IsValid()))
		{
			g_ecaCooldown.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_ecaCooldown[item] = g_ecaCooldown[item] - 1;
			}
			else
			{
				g_ecaCooldown.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_dangerFireNade)
	{
		if (!(item.IsValid()))
		{
			g_dangerFireNade.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_dangerFireNade[item] = g_dangerFireNade[item] - 1;
			}
			else
			{
				g_dangerFireNade.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_dangerFreeze)
	{
		if (!(item.IsValid()))
		{
			g_dangerFreeze.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_dangerFreeze[item] = g_dangerFreeze[item] - 1;
			}
			else
			{
				g_dangerFreeze.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_dangerHeal)
	{
		if (!(item.IsValid()))
		{
			g_dangerHeal.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_dangerHeal[item] = g_dangerHeal[item] - 1;
			}
			else
			{
				g_dangerHeal.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_bigKillBuff)
	{
		if (!(item.IsValid()))
		{
			g_bigKillBuff.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				g_bigKillBuff[item] = g_bigKillBuff[item] - 1;
			}
			else
			{
				g_bigKillBuff.rawdelete(item);
			}
		}
	}
	for (local i = 0 ; i < g_friendlyAliens.len() ; i++)
	{
		if (!(g_friendlyAliens[i].IsValid()))
		{
			g_friendlyAliens.remove(i);
		}
		else
		{
			if (g_friendlyAliens[i].GetName() != "asw_friendly_alien")
			{
				g_friendlyAliens[i].SetName("asw_friendly_alien");
				foreach (classname in g_alienClassnames)
				{
					DoEntFire("!self", "SetRelationship", (classname + " D_HT 5"), 0, null, g_friendlyAliens[i]);
				}
				DoEntFire("!self", "SetRelationship", "asw_marine D_LI 5", 0, null, g_friendlyAliens[i]);
				DoEntFire("!self", "SetRelationship", "asw_bait D_LI 5", 0, null, g_friendlyAliens[i]);
				local friendly = null;
				while ((friendly = Entities.FindByName(friendly, "asw_friendly_alien")) != null)
				{
					DoEntFire("!self", "SetRelationship", "asw_friendly_alien D_LI 5", 0, null, friendly);
				}
			}
		}
	}
	foreach (item, value in g_ghosts)
	{
		if (!(item.IsValid()))
		{
			g_ghosts.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				item.__KeyValueFromInt( "rendermode", 1 );
				item.__KeyValueFromInt( "renderamt", 204 );
				if (g_rorTimer % 10 == 0)
				{
					local victim = GetNearestAlien(item.GetOrigin(), 100.0);
					if (victim != null && CanTrace(item.GetOrigin(), victim.GetOrigin()))
					{
						victim.TakeDamage(item.GetMaxHealth(), 256, item);
					}
				}
				item.Extinguish();
				if (value <= item.GetMaxHealth())
				{
					item.SetHealth(value);
				}
				g_ghosts[item] = g_ghosts[item] - 1;
			}
			else
			{
				item.TakeDamage(item.GetHealth()*5, 0, null);
				g_ghosts.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_queenRemove)
	{
		if (value > 0)
		{
			g_queenRemove[item] = g_queenRemove[item] - 1;
		}
		else
		{
			try
			{
				item.Destroy();
			}catch(error){}
			g_queenRemove.rawdelete(item);
		}
	}
	foreach (item, value in g_noHealing)
	{
		if (!(item.IsValid()))
		{
			g_noHealing.rawdelete(item);
		}
		else
		{
			if (value > 0)
			{
				if (item.GetHealth() > 0)
				{
					SpawnParticle1("vortigaunt_hand_glow", item.GetOrigin() + Vector(0, 0, 50), 0.1, item);
					SpawnParticle1("ad_red_smallfire", item.GetOrigin() + Vector(0, 0, 50), 0.1, item);
				}
				g_noHealing[item] = g_noHealing[item] - 1;
			}
			else
			{
				g_noHealing.rawdelete(item);
			}
		}
	}
	foreach (item, value in g_elites)
	{
		if (!(item.IsValid()))
		{
			g_elites.rawdelete(item);
		}
		else
		{
			if (item.GetHealth() > 0)
			{
				if (value[0] == "typeFire")
				{
					SpawnParticle("powerup_fire_bullets", item.GetOrigin() + Vector(0, 0, 40), 0.1, item);
				}
				else if (value[0] == "typeLightning")
				{
					SpawnParticle("powerup_electric_bullets", item.GetOrigin() + Vector(0, 0, 40), 0.1, item);
				}
				else if (value[0] == "typeEarth")
				{
					SpawnParticle("powerup_chemical_bullets", item.GetOrigin() + Vector(0, 0, 40), 0.1, item);
					if (IsInArray(item, g_friendlyAliens))
					{
						local healTarget = Entities.FindByClassnameNearest("asw_marine", item.GetOrigin(), 300.0);
						if (healTarget != null)
						{
							SpawnParticle("heal_recharged_glow", healTarget.GetOrigin() + Vector(0, 0, 20), 0.1, healTarget);
							Heal(healTarget, 1);
						}
					}
					else
					{
						local healTargets = GetNearestAliens(item.GetOrigin(), 300.0, 2);
						foreach (target in healTargets)
						{
							if (target != null && target != item)
							{
								SpawnParticle("heal_recharged_glow", target.GetOrigin() + Vector(0, 0, 20), 0.1, target);
								if (target.GetMaxHealth()*0.001 < 10)
								{
									Heal(target, 10);
								}
								else
								{
									Heal(target, target.GetMaxHealth()*0.001);
								}
							}
						}
					}
				}
				else if (value[0] == "typeCorruption")
				{
					SpawnParticle("vortigaunt_hand_glow", item.GetOrigin() + Vector(0, 0, 40), 0.1, item);
				}
				else if (value[0] == "typeIncorporeality")
				{
					SpawnParticle("melee_stun", item.GetOrigin() + Vector(0, 0, 40), 0.1, item);
				}
			}
		}
	}
	foreach (item, value in g_bossAttackTracker)
	{
		if (value > 0)
		{
			g_bossAttackTracker[item] = g_bossAttackTracker[item] - 1;
		}
		else
		{
			SpawnParticle("stungrenade_arc_sprites", item);
			SpawnParticle("asw_env_explosion", item);
			SpawnParticle("explosion_shockwave1", item);
			local blastTarget = null;
			local hit = false;
			while ((blastTarget = Entities.FindByClassnameWithin(blastTarget, "asw_marine", item, 100.0)) != null)
			{
				if (CanTrace(item, blastTarget.GetOrigin()))
				{
					local bossEnt = null;
					bossEnt = Entities.FindByName(bossEnt, "asw_boss_ror");
					blastTarget.TakeDamage(50, 64, bossEnt);
					hit = true;
				}
			}
			local randomChance = RandomInt(0, 9);
			local alienName = "";
			if (randomChance == 0)
			{
				alienName = g_bossSummons2[RandomInt(0, g_bossSummons2.len()-1)];
			}
			else
			{
				alienName = g_bossSummons1[RandomInt(0, g_bossSummons1.len()-1)];
			}
			local alien = Director.SpawnAlienAt(alienName, item + Vector(0, 0, 20), Vector(0.0, RandomFloat(0.0, 360.0), 0.0));
			alien.EmitSound("explosions/teleport_boom01.wav");
			if (hit)
			{
				alien.Destroy();
			}
			else
			{
				alien.SetHealth(alien.GetMaxHealth()*0.5);
				alien.SetMaxHealth(alien.GetMaxHealth()*0.5);
				alien.ChaseNearestMarine();
			}
			g_bossAttackTracker.rawdelete(item);
		}
	}
	foreach (item, value in g_bossAttackQueue)
	{
		if (!(item.IsValid()))
		{
			g_bossAttackQueue.rawdelete(item);
		}
		else
		{
			if (!(value[1].IsValid()))
			{
				g_bossAttackQueue.rawdelete(item);
			}
			else if (value[0] > 0)
			{
				if (value[0] == 7 || value[0] == 4 || value[0] == 1)
				{
					BossAttack(item, value[1].GetOrigin(), value[1]);
				}
				g_bossAttackQueue[item][0] = g_bossAttackQueue[item][0] - 1;
			}
			else
			{
				g_bossAttackQueue.rawdelete(item);
			}
		}
	}
	foreach (name in g_alienClassnames)
	{
		local alien = null;
		while ((alien = Entities.FindByClassname(alien, name)) != null)
		{
			if (!(alien in g_elites && g_elites[alien][0] == "typeIncorporeality") && !(alien in g_ghosts))
			{
				if (CelestineNearby(alien.GetOrigin()))
				{
					alien.__KeyValueFromInt( "rendermode", 1 );
					alien.__KeyValueFromInt( "renderamt", 0 );
				}
				else
				{
					alien.__KeyValueFromInt( "renderamt", 255 );
				}
			}
		}
	}
	local timeText = "";
	if (g_gameRunning)
	{
		if (UnitToTime(g_rorTimer)[1] < 10)
		{
			timeText = timeText + "0";
		}
		timeText = timeText + UnitToTime(g_rorTimer)[1].tostring() + ":";
		if (UnitToTime(g_rorTimer)[2] < 10)
		{
			timeText = timeText + "0";
		}
		timeText = timeText + UnitToTime(g_rorTimer)[2].tostring();
		foreach (name in g_marineNames)
		{
			local hRorHud = Entities.FindByName( null, "#asw_name_" + name.tolower() + "_hud" );
			if (hRorHud != null)
			{
				hRorHud.SetString(0, GetDiffText(false) + "|" + timeText);
				hRorHud.SetInt(0, g_rorStage);
				hRorHud.SetInt(1, GetAlienLevel());
				hRorHud.SetInt(2, GetMarineLevel());
				local bossEnt = null;
				bossEnt = Entities.FindByName(bossEnt, "asw_boss_ror");
				if (bossEnt != null && bossEnt.GetHealth() > 0)
				{
					hRorHud.SetInt(3, bossEnt.GetHealth());
					hRorHud.SetInt(4, bossEnt.GetMaxHealth());
				}
				else
				{
					hRorHud.SetInt(3, 0);
					hRorHud.SetInt(4, 0);
				}
			}
		}
	}
	local marine = null;
	while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
	{
		local invTable = {};
		marine.GetInventoryTable(invTable);
		if ("slot3" in invTable)
		{
			if (invTable["slot3"] in g_rorItemPickups)
			{
				local itemName = invTable["slot3"].GetClassname();
				local itemTier = g_rorItemPickups[invTable["slot3"]][0];
				g_rorItemPickups.rawdelete(invTable["slot3"]);
				marine.RemoveWeapon(3);
				RorAddItem(marine.GetMarineName(), itemTier, itemName);
				local itemText = "";
				if (itemTier == "tierWhite")
				{
					itemText = COLOR_WHITE + "Common " + ClassnameToName(itemName);
				}
				else if (itemTier == "tierGreen")
				{
					itemText = COLOR_GREEN + "Uncommon " + ClassnameToName(itemName);
				}
				else if (itemTier == "tierRed")
				{
					itemText = COLOR_RED + "Legendary " + ClassnameToName(itemName);
				}
				local player = null;
				while((player = Entities.FindByClassname(player, "player")) != null)
				{
					if (player == marine.GetCommander())
					{
						ClientPrint(player, 3, "#ror_itemPickup", COLOR_TEXT, itemText + COLOR_TEXT, RorItemDesc(itemTier, itemName));
						local newCount = RorCountItem(marine.GetMarineName(), itemTier, itemName);
						if (newCount != 1)
						{
							ClientPrint(player, 3, "#ror_newItemCount", COLOR_TEXT, COLOR_YELLOW + newCount.tostring() + COLOR_TEXT, itemText + COLOR_TEXT);
						}
						else
						{
							ClientPrint(player, 3, "#ror_newItemCount_1", COLOR_TEXT, COLOR_YELLOW + newCount.tostring() + COLOR_TEXT, itemText + COLOR_TEXT);
						}
					}
					else
					{
						ClientPrint(player, 3, "#ror_itemPickupOther", COLOR_GREY + marine.GetCommander().GetPlayerName(), itemText + COLOR_GREY, RorItemDesc(itemTier, itemName));
					}
				}
			}
		}
		ProcPassive(marine);
	}
	local boss = null;
	while ((boss = Entities.FindByName(boss, "asw_boss_ror")) != null)
	{
		if (g_rorTimer % 40 == 0)
		{
			local target = null;
			target = Entities.FindByClassnameNearest("asw_marine", boss.GetOrigin(), 600.0);
			if (target != null)
			{
				g_bossAttackQueue[boss] <- [7,  target];
			}
		}
	}
	if (g_bossKey != null)
	{
		local bossKeyGlow = null;
		while ((bossKeyGlow = Entities.FindByName(bossKeyGlow, "asw_ror_bossKeyGlow")) != null)
		{
			if (g_bossKey.GetMoveParent() != null && g_bossKey.GetMoveParent().GetClassname() == "asw_marine")
			{
				if (!(g_bossKeyPickup[0]))
				{
					g_bossKeyPickup[0] = true;
				}
				bossKeyGlow.SetOrigin(Vector(0, 0, 50));
			}
			else
			{
				bossKeyGlow.SetOrigin(Vector(0, 0, 0));
			}
		}
	}
	if (g_bossKey != null && !(g_bossKeyPickup[0]))
	{
		if ((g_bossKey.GetOrigin() - g_bossKeyPickup[1]).Length() > 20)
		{
			g_bossKey.SetOrigin(g_bossKeyPickup[1]);
			g_bossKey.SetVelocity(Vector(0, 0, 0));
		}
		g_bossKey.SetAnglesVector(g_bossKey.GetAngles() + Vector(0, 10, 0));
	}
	if (g_gameRunning)
	{
		g_rorTimer = g_rorTimer + 1;
	}
	return 0.1;
}

function OnGameEvent_alien_spawn(params)
{
	if (!("entindex" in params))
	{
		return;
	}
	local alien = EntIndexToHScript(params["entindex"]);
	local hpbar = Entities.CreateByClassname( "asw_health_bar" );
	hpbar.__KeyValueFromString("StartDisabled", "0");
	hpbar.__KeyValueFromString("color", "255 0 0 255");
	hpbar.__KeyValueFromString("hideatfullhealth", "1");
	if (alien.GetMaxHealth() <= 104)
	{
		hpbar.__KeyValueFromString("scale", "0.75");
	}
	else
	{
		hpbar.__KeyValueFromString("scale", "1.0");
	}
	hpbar.SetOrigin(alien.GetOrigin()+Vector(0, 0, 50));
	hpbar.SetParent(alien);
	hpbar.SetName("asw_hpbar_ror");
	hpbar.Spawn();
	hpbar.Activate();
	if (IsInArray(alien.GetClassname(), g_eliteBlacklist))
	{
		return;
	}
	TurnElite(alien);
	if (alien in g_elites)
	{
		hpbar.__KeyValueFromString("color", "255 0 255 255");
	}
}

function OnTakeDamage_Alive_Any( victim, inflictor, attacker, weapon, damage, damageType, ammoName )
{
	if (attacker != null && attacker in g_setDamage)
	{
		damage = g_setDamage[attacker];
	}
	if (victim != null && victim.IsAlien())
	{
		if (victim in g_ghosts && attacker != null && attacker != victim)
		{
			return;
		}
		if (attacker != null && (victim.GetName() == "asw_boss_ror" || victim.GetName() == "asw_queen_ror") && !(attacker.GetClassname() == "asw_marine" || Entities.FindByClassnameNearest("asw_marine", victim.GetOrigin(), 600.0) != null))
		{
			return;
		}
		if (attacker == null || attacker == victim)
		{
			return damage;
		}
		if (victim.GetName() == "asw_boss_ror" && (attacker.IsAlien() || attacker.GetClassname() == "asw_boomer_blob") && !(IsInArray(attacker, g_friendlyAliens)))
		{
			return;
		}
		if (victim.GetName() != "asw_boss_ror")
		{
			damage = damage/(1+0.3*GetAlienLevel());
		}
		if (attacker.GetClassname() == "asw_marine")
		{
			if (IsInArray(victim, g_friendlyAliens))
			{
				return;
			}
			damage = damage*(1+0.2*GetMarineLevel());
			if (weapon != null && !(inflictor != null && inflictor.GetClassname() == "asw_burning"))
			{
				try
				{
					damage = ProcOffense(attacker, victim, damage, damageType);
				}catch(error){}
			}
		}
		else if (IsInArray(attacker.GetName(), g_marineNames) && !(IsInArray(attacker, g_friendlyAliens)))
		{
			damage = damage*(1+0.2*GetMarineLevel());
			try
			{
				damage = ProcOffense(GetMarineByName(attacker.GetName()), victim, damage, damageType);
			}catch(error){}
		}
		else if (attacker.GetClassname() == "asw_sentry_top_machinegun" || IsInArray(attacker, g_friendlyAliens))
		{
			damage = damage*(1+0.2*GetAlienLevel());
			if (attacker in g_elites)
			{
				if (g_elites[attacker][1] == 1)
				{
					damage = damage*2;
					if (g_elites[attacker][0] == "typeFire")
					{
						victim.Ignite(3+damage/10);
					}
					if (g_elites[attacker][0] == "typeLightning" && damageType != 0)
					{
						SpawnParticle("stungrenade_arc_sprites", victim.GetOrigin());
						victim.EmitSound(g_teslaSfx[RandomInt(0,g_teslaSfx.len()-1)]);
						local blastTarget = null;
						while ((blastTarget = Entities.FindInSphere(blastTarget, victim.GetOrigin(), 100.0)) != null)
						{
							if (blastTarget != attacker && blastTarget.IsAlien() && CanTrace(attacker.GetOrigin(), blastTarget.GetOrigin()))
							{
								blastTarget.ElectroStun(5.0);
								blastTarget.TakeDamage(damage*0.5, 0, attacker);
							}
						}
					}
				}
				else if (g_elites[attacker][1] == 2)
				{
					damage = damage*6;
					if (g_elites[attacker][0] == "typeCorruption")
					{
						g_noHealing[victim] <- 80;
					}
				}
			}
		}
		try
		{
			if (victim.GetName() == "asw_boss_ror" && damage > (victim.GetMaxHealth()*0.3))
			{
				damage = victim.GetMaxHealth()*0.3;
			}
		}catch(error){}
	}
	else if (victim != null && victim.GetClassname() == "asw_marine")
	{
		if (attacker == null && damageType != null && damageType == 33554432)
		{
			g_inDanger[victim] <- 70;
			return damage;
		}
		if (attacker != null && attacker.GetClassname() != "asw_marine" && (IsInArray(attacker.GetName(), g_marineNames) || IsInArray(attacker, g_friendlyAliens)))
		{
			return;
		}
		if (attacker != null && attacker.GetName() == "asw_boss_ror" && inflictor != null && attacker == inflictor && damageType == 4)
		{
			damage = 25;
		}
		damage = damage/(1+0.3*GetMarineLevel());
		if (attacker != null && (attacker.IsAlien() || attacker.GetClassname() == "asw_boomer_blob"))
		{
			damage = damage*(1+0.2*GetAlienLevel());
			if (attacker in g_elites)
			{
				if (g_elites[attacker][1] == 1)
				{
					damage = damage*2;
					if (g_elites[attacker][0] == "typeFire")
					{
						victim.Ignite(3+damage/10);
					}
					if (g_elites[attacker][0] == "typeLightning" && damageType != 256)
					{
						SpawnParticle1("stungrenade_arc_sprites", victim.GetOrigin());
						victim.EmitSound(g_teslaSfx[RandomInt(0,g_teslaSfx.len()-1)]);
						local blastTarget = null;
						while ((blastTarget = Entities.FindByClassnameWithin(blastTarget, "asw_marine", victim.GetOrigin(), 100.0)) != null)
						{
							if (CanTrace(attacker.GetOrigin(), blastTarget.GetOrigin()))
							{
								blastTarget.TakeDamage(damage*0.5, 256, attacker);
							}
						}
					}
				}
				else if (g_elites[attacker][1] == 2)
				{
					damage = damage*6;
					if (g_elites[attacker][0] == "typeCorruption")
					{
						g_noHealing[victim] <- 80;
					}
				}
			}
			try
			{
				damage = ProcDefense(victim, attacker, damage, damageType);
			}catch(error){}
		}
		else if (attacker != null && attacker.GetClassname() == "asw_marine")
		{
			damage = damage*(1+0.2*GetMarineLevel());
			if (attacker == victim)
			{
				try
				{
					damage = ProcDefense(victim, victim, damage, damageType);
				}catch(error){}
			}
		}
		if (damage > (victim.GetMaxHealth()*0.9))
		{
			damage = victim.GetMaxHealth()*0.9;
		}
		ProcDanger(victim, attacker, damage, damageType);
	}
	return damage;
}

function OnGameEvent_entity_killed( params )
{
	local victim = EntIndexToHScript( params["entindex_killed"] );
	local attacker = null;
	if ( "entindex_attacker" in params )
	{
		attacker = EntIndexToHScript( params["entindex_attacker"] );
	}

	if ( !victim )
		return;

	if (victim in g_killed)
	{
		return;
	}
	else
	{
		g_killed[victim] <- attacker;
	}

	local particle = null;
	while((particle = Entities.FindByClassname(particle, "info_particle_system")) != null)
	{
		if (particle.GetMoveParent() == victim)
		{
			particle.Destroy();
		}
	}

	if (victim.GetName() == "asw_queen_ror")
	{
		g_queenKills = g_queenKills + 1;
		EntFireByHandle( g_queenObj, "SetProgress", g_queenKills.tostring(), 0.0, null, null );
		if (g_queenCount > 0 && g_queenKills >= g_queenCount && g_bossKey == null)
		{
			EntFireByHandle( g_queenObj, "SetComplete", "", 0.0, null, null );
			g_keyObj.__KeyValueFromInt("Visible", 1);
			g_bossKey = Entities.CreateByClassname("asw_weapon_flashlight");
			local nearestMarine = Entities.FindByClassnameNearest("asw_marine", victim.GetOrigin(), 1500.0);
			local spawnOrigin = victim.GetOrigin();
			if (nearestMarine != null)
			{
				spawnOrigin = nearestMarine.GetOrigin();
			}
			g_bossKey.SetOrigin(spawnOrigin);
			g_bossKeyPickup[1] = spawnOrigin;
			while (!(IsOnGround(g_bossKey)))
			{
				g_bossKeyPickup[1] = g_bossKeyPickup[1] + Vector(0, 0, -1);
				g_bossKey.SetOrigin(g_bossKeyPickup[1]);
			}
			g_bossKey.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
			g_bossKey.Spawn();
			local hSprite = Entities.CreateByClassname( "env_sprite" );
			hSprite.__KeyValueFromInt( "spawnflags", 1 );
			hSprite.__KeyValueFromString( "Model", "materials/Sprites/light_glow03.vmt" );
			hSprite.__KeyValueFromString( "rendercolor", "255 255 0" );
			hSprite.__KeyValueFromFloat( "GlowProxySize", 24 );
			hSprite.__KeyValueFromInt( "renderamt", 192 );
			hSprite.__KeyValueFromInt( "rendermode", 9 );
			hSprite.__KeyValueFromFloat( "scale", 1.0 );
			hSprite.SetOrigin( g_bossKey.GetOrigin() );
			hSprite.SetParent( g_bossKey );
			hSprite.Spawn();
			hSprite.Activate();
			hSprite.SetName("asw_ror_bossKeyGlow");
			ClientPrint(null, 3, "#ror_bossKeyDropped");
		}
		if (!(victim in g_queenRemove))
		{
			g_queenRemove[victim] <- 60;
		}
	}

	if (victim.GetName() == "asw_boss_ror")
	{
		EntFireByHandle( g_bossObj, "SetComplete", "", 0.0, null, null );
		g_bossKilled = true;
		ClientPrint(null, 3, "#ror_bossKilled");
	}

	if ( !attacker )
		return;
	
	if ( (attacker.GetClassname() == "asw_marine" || attacker.GetClassname() == "asw_sentry_top_machinegun" || IsInArray(attacker.GetName(), g_marineNames) || IsInArray(attacker, g_friendlyAliens)) && victim.IsAlien() )
	{
		local eliteFactor = 1;
		if (victim in g_elites)
		{
			if (g_elites[victim][1] == 1)
			{
				eliteFactor = 6;
			}
			else if (g_elites[victim][1] == 2)
			{
				eliteFactor = 36;
			}
		}
		g_rorMarineExp = g_rorMarineExp + GetCoeff() * GetAlienLevel()/2 * eliteFactor;
		local rorItemDrop = null;
		local rorItemCount = 0;
		while((rorItemDrop = Entities.FindInSphere(rorItemDrop, victim.GetOrigin(), 600.0)) != null)
		{
			if (rorItemDrop in g_rorItemPickups)
			{
				rorItemCount = rorItemCount + 1;
			}
		}
		local dropped = false;
		if (!(g_bossSpawned) && rorItemCount < 4)
		{
			if (victim.GetName() == "asw_queen_ror")
			{
				local randomChance = RandomInt(0, 90);
				local tier = "tierWhite";
				if (randomChance >= 0 && randomChance <= 62)
				{
					tier = "tierGreen";
				}
				else if (randomChance >= 63 && randomChance <= 89)
				{
					tier = "tierRed";
				}
				dropped = RorDropItem(victim.GetOrigin(), tier).IsValid();
			}
			else
			{
				local playerCountFactor = 0.5 + 0.5*g_activeMarines.len();
				local spawnruleFactor = 1;
				if (Convars.GetStr("rd_override_alien_selection_challenge") != "asbi")
				{
					spawnruleFactor = 2;
				}
				local randomChance = RandomInt(0, (((19+g_rorItemSpawnCount*5/playerCountFactor*spawnruleFactor)*g_carnageNumber+g_carnageNumber-1)/eliteFactor*spawnruleFactor).tointeger());
				if (randomChance == 0 && Entities.FindByClassnameNearest("asw_marine", victim.GetOrigin(), 1200.0) != null)
				{
					dropped = RorDropItem(victim.GetOrigin()).IsValid();
				}
			}
		}
		if (dropped)
		{
			g_rorItemSpawnCount = g_rorItemSpawnCount + 1;
		}
		ProcKill(attacker, victim, dropped);
		g_totalKills = g_totalKills + 1;
	}
}

function OnGameEvent_asw_mission_restart( params )
{
	if (g_gameRunning)
	{
		foreach (name in g_activeMarines)
		{
			InspectInventory(null, name);
		}
		RorResetKeepInventory();
		ClientPrint(null, 3, "#ror_everyInvListed");
		if (g_rorStage-1 != 1)
		{
			ClientPrint(null, 3, "#ror_progressReport_gameOver", COLOR_RED, (g_rorStage-1).tostring(), TimeReport(), GetDiffText());
		}
		else
		{
			ClientPrint(null, 3, "#ror_progressReport_gameOver_1", COLOR_RED, (g_rorStage-1).tostring(), TimeReport(), GetDiffText());
		}
		ClientPrint(null, 3, "#ror_totalKills", COLOR_TEXT, COLOR_RED + g_totalKills.tostring() + COLOR_TEXT);
	}
	g_gameRunning = false;
}
function OnGameEvent_mission_failed( params )
{
	if (g_gameRunning)
	{
		foreach (name in g_activeMarines)
		{
			InspectInventory(null, name);
		}
		RorResetKeepInventory();
		ClientPrint(null, 3, "#ror_everyInvListed");
		if (g_rorStage-1 != 1)
		{
			ClientPrint(null, 3, "#ror_progressReport_gameOver", COLOR_RED, (g_rorStage-1).tostring(), TimeReport(), GetDiffText());
		}
		else
		{
			ClientPrint(null, 3, "#ror_progressReport_gameOver_1", COLOR_RED, (g_rorStage-1).tostring(), TimeReport(), GetDiffText());
		}
		ClientPrint(null, 3, "#ror_totalKills", COLOR_TEXT, COLOR_RED + g_totalKills.tostring() + COLOR_TEXT);
	}
	g_gameRunning = false;
}

function OnGameEvent_mission_success( params )
{
	if (g_bossKilled)
	{
		foreach (name in g_activeMarines)
		{
			InspectInventory(null, name);
		}
		RorResetKeepInventory();
		ClientPrint(null, 3, "#ror_everyInvListed");
		if (g_rorStage != 1)
		{
			ClientPrint(null, 3, "#ror_progressReport_victory", COLOR_GREEN, g_rorStage.tostring(), TimeReport(), GetDiffText());
		}
		else
		{
			ClientPrint(null, 3, "#ror_progressReport_victory_1", COLOR_GREEN, g_rorStage.tostring(), TimeReport(), GetDiffText());
		}
		ClientPrint(null, 3, "#ror_totalKills", COLOR_TEXT, COLOR_RED + g_totalKills.tostring() + COLOR_TEXT);
	}
	else
	{
		RorSave();
	}
	g_gameRunning = false;
}

function OnGameEvent_player_say(params)
{
	if (!("text" in params) || params["text"] == null || !("userid" in params) || params["userid"] == null)
		return;
	
	local text = params["text"].tolower();
	local player = GetPlayerFromUserID(params["userid"]);

	switch (text){
		case "/i":
			InspectInventory(player);
			return;
		case "/restart":
			if (!g_gameRunning)
			{
				RorReset();
				ClientPrint(null, 3, "Game has been reset by player command");
			}
			else
			{
				ClientPrint(player, 3, "Cannot use /restart command during gameplay. Only available in lobby.");
			}
			return;
		case "/ammo":
			if (player != null && player.GetMarine() != null)
			{
				local marineName = player.GetMarine().GetMarineName();
				RorAddItem(marineName, "tierGreen", "asw_weapon_smart_bomb", 1);
				ClientPrint(player, 3, "#ror_itemPickup", COLOR_TEXT, COLOR_GREEN + "Uncommon " + ClassnameToName("asw_weapon_smart_bomb") + " ×1" + COLOR_TEXT, RorItemDesc("tierGreen", "asw_weapon_smart_bomb"));
				ClientPrint(player, 3, "#ror_newItemCount_1", COLOR_TEXT, COLOR_YELLOW + "1" + COLOR_TEXT, COLOR_GREEN + "Uncommon " + ClassnameToName("asw_weapon_smart_bomb") + COLOR_TEXT);
			}
			return;
		case "/med":
			if (player != null && player.GetMarine() != null)
			{
				local marineName = player.GetMarine().GetMarineName();
				RorAddItem(marineName, "tierWhite", "asw_weapon_medkit", 1);
				ClientPrint(player, 3, "#ror_itemPickup", COLOR_TEXT, COLOR_WHITE + "Common " + ClassnameToName("asw_weapon_medkit") + " ×1" + COLOR_TEXT, RorItemDesc("tierWhite", "asw_weapon_medkit"));
				ClientPrint(player, 3, "#ror_newItemCount_1", COLOR_TEXT, COLOR_YELLOW + "1" + COLOR_TEXT, COLOR_WHITE + "Common " + ClassnameToName("asw_weapon_medkit") + COLOR_TEXT);
			}
			return;
		default: return;
	}
	return;
}

function OnGameEvent_button_area_used( params )
{
	local marine = GetPlayerFromUserID(params["userid"]).GetMarine();
	local button = params["entindex"];
	if (!(IsInArray(button, g_usedButtons)))
	{
		if (RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_hornet_barrage") > 0)
		{
			QueueRocket(marine, 250, 4+4*RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_hornet_barrage"));
		}
		g_usedButtons.append(button);
	}
}

function ProcInit(subject)
{
	if (RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_smart_bomb") > 0)
	{
		local invTable = {};
		subject.GetInventoryTable(invTable);
		if ("slot2" in invTable)
		{
			if (invTable["slot2"].Clip1() > 0)
			{
				invTable["slot2"].SetClip1(invTable["slot2"].Clip1() + RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_smart_bomb"));
			}
		}
	}
}

function ProcOffense(attacker, victim, damage, damageType)
{
	if ( !attacker || !victim )
	{
		return damage;
	}
	local damageMod = 1.0;
	local critChance = 0;
	local instakill = false;
	if (IsSomeoneHacking())
	{
		local marine = null;
		local whiteWelderBuff = false;
		while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
		{
			if (RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder") > 0 && (marine.GetOrigin() - attacker.GetOrigin()).Length() <= 50 + 50*RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder"))
			{
				whiteWelderBuff = true;
			}
		}
		if (whiteWelderBuff)
		{
			damageMod = damageMod + 0.5;
		}
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_flares") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < 10*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_flares")))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < 10*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_flares"))
		{
			victim.Ignite(5.0);
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_buff_grenade") > 0)
	{
		if (victim.GetHealth() > victim.GetMaxHealth()*0.9)
		{
			damageMod = damageMod + 0.75*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_buff_grenade");
		}
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && victim.GetName() != "asw_boss_ror" && RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_freeze_grenades") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_freeze_grenades")/(0.05*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_freeze_grenades") + 1)))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_freeze_grenades")/(0.05*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_freeze_grenades") + 1))
		{
			victim.Freeze(2.0);
		}
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_tesla_trap") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_tesla_trap")))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_tesla_trap"))
		{
			victim.ElectroStun(2.0);
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_fist") > 0)
	{
		if ((victim.GetOrigin() - attacker.GetOrigin()).Length() <= 100)
		{
			damageMod = damageMod + 0.2*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_fist");
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_grenades") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_grenades")))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < 5*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_grenades"))
		{
			local grenade = DropFragGrenade(128.0, 280.0, victim.GetOrigin() + Vector(0, 0, 5));
			grenade.SetName(attacker.GetMarineName());
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_night_vision") > 0)
	{
		critChance = critChance + 10*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_night_vision");
	}
	if (RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_jump_jet") > 0)
	{
		if (IsInArray(victim.GetClassname(), g_largeAlienClassnames))
		{
			damageMod = damageMod + 0.1*RorCountItem(attacker.GetMarineName(), "tierWhite", "asw_weapon_jump_jet");
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_medkit") > 0)
	{
		critChance = critChance + 5;
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_flares") > 0)
	{
		if (NetProps.GetPropBool(victim, "m_bOnFire"))
		{
			damageMod = damageMod + 0.2*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_flares");
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_normal_armor") > 0)
	{
		if (attacker in g_frenzy)
		{
			damageMod = damageMod + 0.8;
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_buff_grenade") > 0)
	{
		critChance = critChance + 5;
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_hornet_barrage") > 0)
	{
		local randomChance = RandomInt(0, 9);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (randomChance != 0)
			{
				randomChance = RandomInt(0, 9);
			}
		}
		if (randomChance == 0)
		{
			QueueRocket(attacker, 250*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_hornet_barrage"));
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_stim") > 0)
	{
		local randomChance = RandomInt(0, 1);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (randomChance != 0)
			{
				randomChance = RandomInt(0, 1);
			}
		}
		if (randomChance == 0)
		{
			if (attacker.GetHealth() < attacker.GetMaxHealth())
			{
				SpawnParticle("heal_recharged", attacker.GetOrigin() + Vector(0, 0, 50), 0.1, attacker);
				Heal(attacker, (1*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_stim")));
			}
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_tesla_trap") > 0)
	{
		local randomChance = RandomInt(0, 9);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (randomChance != 0)
			{
				randomChance = RandomInt(0, 9);
			}
		}
		if (randomChance == 0)
		{
			SpawnParticle("stungrenade_arc_sprites", victim.GetOrigin());
			victim.EmitSound(g_teslaSfx[RandomInt(0,g_teslaSfx.len()-1)]);
			local stunTarget = null;
			while ((stunTarget = Entities.FindInSphere(stunTarget, victim.GetOrigin(), 50 + 50*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_tesla_trap"))) != null)
			{
				if (stunTarget.IsAlien() && !(IsInArray(stunTarget, g_friendlyAliens)))
				{
					stunTarget.ElectroStun(2.0);
				}
			}
		}
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_night_vision") > 0)
	{
		if (NetProps.GetPropBool(victim, "m_bOnFire") && NetProps.GetPropBool(victim, "m_bElectroStunned"))
		{
			g_deathMarked[victim] <- 70*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_night_vision");
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_jump_jet") > 0)
	{
		if (!(attacker in g_inDanger))
		{
			damageMod = damageMod + 0.4*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_jump_jet");
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_normal_armor") > 0)
	{
		if (attacker in g_bigKillBuff)
		{
			critChance = critChance + 100;
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_mines") > 0)
	{
		critChance = critChance + 5;
		if (NetProps.GetPropBool(victim, "m_bOnFire") && !(IsInArray(victim, g_friendlyAliens)))
		{
			g_burnExplode[victim] <- attacker;
		}
	}
	local critRoll = RandomInt(0, 99);
	for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
	{
		if (!(critRoll >= 0 && critRoll <= critChance))
		{
			critRoll = RandomInt(0, 99);
		}
	}
	if (critRoll >= 0 && critRoll <= critChance)
	{
		damage = damage * (2 + RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_buff_grenade"));
		SpawnParticle("hunter_projectile_explosion_2g", victim.GetOrigin() + Vector(0, 0, 20));
		SpawnParticle("hunter_projectile_explosion_2f", victim.GetOrigin() + Vector(0, 0, 20));
		attacker.EmitSound("swarm/interface/upgrade.wav");
		if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_medkit") > 0)
		{
			if (attacker.GetHealth() < attacker.GetMaxHealth())
			{
				SpawnParticle("heal_recharged", attacker.GetOrigin() + Vector(0, 0, 50), 0.1, attacker);
				Heal(attacker, (2/(1+0.3*GetMarineLevel())+2*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_medkit")));
			}
		}
		if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_mines") > 0)
		{
			victim.Ignite(5.0);
			g_burnExplode[victim] <- attacker;
		}
	}
	damage = damage * damageMod;
	if (critRoll >= 0 && critRoll <= critChance)
	{
		if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_buff_grenade") > 0)
		{
			local linkTargets = GetNearestAliens(victim.GetOrigin(), 300.0, 1 + RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_buff_grenade"));
			foreach (target in linkTargets)
			{
				if (target != null && target != victim)
				{
					SpawnParticle("combine_ball_bounce", target.GetOrigin() + Vector(0, 0, 20));
					target.TakeDamage(damage*0.8, damageType, attacker);
				}
			}
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_welder") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < (100 - 100/(1 + 0.2 * RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_welder")))))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < (100 - 100/(1 + 0.2 * RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_welder"))))
		{
			victim.EmitSound(g_stompSfx[RandomInt(0,g_stompSfx.len()-1)]);
			local hookTarget = null;
			local hookCount = 5 + 5*RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_welder");
			while ((hookTarget = Entities.FindInSphere(hookTarget, victim.GetOrigin(), 300.0)) != null)
			{
				if (hookCount > 0 && hookTarget.IsAlien() && hookTarget.GetHealth() > 0 && hookTarget != victim && !(IsInArray(hookTarget, g_friendlyAliens)) && CanTrace(victim.GetOrigin(), hookTarget.GetOrigin()))
				{
					SpawnParticle("melee_ground_pound", hookTarget.GetOrigin());
					local dirVector = victim.GetOrigin() - hookTarget.GetOrigin();
					local dirVectorNoZ = Vector(dirVector.x, dirVector.y, 0);
					if (dirVectorNoZ.Length() != 0)
					{
						local dirVectorNoZNormalized = dirVectorNoZ * (1/dirVectorNoZ.Length());
						local pushVector = dirVectorNoZNormalized * dirVector.Length() + Vector(0, 0, 300);
						hookTarget.SetVelocity(pushVector);
					}
					hookTarget.TakeDamage(80, 0, attacker);
					hookCount = hookCount - 1;
				}
			}
		}
	}
	if (RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_laser_mines") > 0)
	{
		local randomChance = RandomInt(0, 9);
		for (local i = 0 ; i < RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_blink") ; i++)
		{
			if (randomChance != 0)
			{
				randomChance = RandomInt(0, 9);
			}
		}
		if (randomChance == 0)
		{
			SpawnParticle("stungrenade_core_arcs", victim.GetOrigin());
			victim.EmitSound(g_lightningSfx[RandomInt(0,g_lightningSfx.len()-1)]);
			local strikeTarget = null;
			while ((strikeTarget = Entities.FindInSphere(strikeTarget, victim.GetOrigin(), 30.0)) != null)
			{
				if (strikeTarget.IsAlien())
				{
					strikeTarget.TakeDamage(1000*RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_laser_mines"), 64, attacker);
				}
			}
		}
	}
	if (victim in g_takeExtraDamage)
	{
		damage = damage * (1 + g_takeExtraDamage[victim]);
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_jump_jet") > 0)
	{
		if (victim in g_takeExtraDamage)
		{
			g_takeExtraDamage[victim] = g_takeExtraDamage[victim] + 0.02;
		}
		else
		{
			g_takeExtraDamage[victim] <- 0.02;
		}
		SpawnParticle("mining_laser_beam_cp0_glow", victim.GetOrigin() + Vector(0, 0, 75), 0.1);
	}
	if (!(IsInArray(victim, g_friendlyAliens)) && RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_fist") > 0)
	{
		SpawnParticle("explosion_air_small", victim.GetOrigin());
		victim.EmitSound(g_smallExplodeSfx[RandomInt(0,g_smallExplodeSfx.len()-1)]);
		local blastTarget = null;
		while ((blastTarget = Entities.FindInSphere(blastTarget, victim.GetOrigin(), 50 + 50*RorCountItem(attacker.GetMarineName(), "tierRed", "asw_weapon_fist"))) != null)
		{
			if (blastTarget.IsAlien() && CanTrace(victim.GetOrigin(), blastTarget.GetOrigin()))
			{
				blastTarget.TakeDamage(damage*0.6, 64, attacker);
			}
		}
	}
	if (victim in g_deathMarked)
	{
		damage = damage * 1.5;
	}
	if (RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_laser_mines") > 0)
	{
		if (victim in g_elites && victim.GetHealth() - damage < victim.GetMaxHealth()*(0.13*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_laser_mines")/(0.13*RorCountItem(attacker.GetMarineName(), "tierGreen", "asw_weapon_laser_mines") + 1)))
		{
			instakill = true;
		}
	}
	if (instakill)
	{
		damage = damage + victim.GetHealth()*5;
	}
	return damage;
}

function ProcDefense(victim, attacker, damage, damageType)
{
	if ( !victim || !attacker )
	{
		return damage;
	}
	if (RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_laser_mines") > 0)
	{
		if (!(victim in g_inDanger))
		{
			SpawnParticle("asw_env_explosion", victim.GetOrigin());
			SpawnParticle("explosion_shockwave1", victim.GetOrigin());
			victim.EmitSound(g_bigExplodeSfx[RandomInt(0,g_bigExplodeSfx.len()-1)]);
			local blastTarget = null;
			while ((blastTarget = Entities.FindInSphere(blastTarget, victim.GetOrigin(), 80 + 20*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_laser_mines"))) != null)
			{
				if (blastTarget.IsAlien() && CanTrace(victim.GetOrigin(), blastTarget.GetOrigin()))
				{
					blastTarget.TakeDamage(damage*5, 64, victim);
				}
			}
		}
	}
	local damageMod = 1.0;
	local noDamage = false;
	if (IsSomeoneHacking())
	{
		local marine = null;
		local whiteWelderBuff = false;
		while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
		{
			if (RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder") > 0 && (marine.GetOrigin() - victim.GetOrigin()).Length() <= 50 + 50*RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder"))
			{
				whiteWelderBuff = true;
			}
		}
		if (whiteWelderBuff)
		{
			damageMod = damageMod + 0.5;
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_normal_armor") > 0)
	{
		damageMod = damageMod + 0.1*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_normal_armor");
	}
	if (RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_stim") > 0)
	{
		g_healQueue[victim] <- 20;
	}
	if (RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_electrified_armor") > 0)
	{
		if (!(victim in g_inDanger))
		{
			damageMod = damageMod + 0.8*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_electrified_armor");
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_blink") > 0)
	{
		local randomChance = RandomInt(0, 99);
		if (randomChance >= 0 && randomChance < 15*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_blink")/(0.15*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_blink") + 1))
		{
			noDamage = true;
			SpawnParticle("powerup_pickup_generic", victim.GetOrigin() + Vector(0, 0, 50));
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_normal_armor") > 0)
	{
		if (victim in g_frenzy)
		{
			damageMod = damageMod + 0.5;
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_electrified_armor") > 0)
	{
		local razorTarget = null;
		local razorCount = 1 + 2*RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_electrified_armor");
		while ((razorTarget = Entities.FindInSphere(razorTarget, victim.GetOrigin(), 150 + 50*RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_electrified_armor"))) != null)
		{
			if (razorCount > 0 && razorTarget.IsAlien() && !(IsInArray(razorTarget, g_friendlyAliens)) && CanTrace(victim.GetOrigin(), razorTarget.GetOrigin()))
			{
				SpawnParticle("thorns_marine_buff_start", razorTarget.GetOrigin() + Vector(0, 0, 20));
				razorTarget.EmitSound("weapons/3d/welder/welddeny.wav");
				razorTarget.TakeDamage(128, 0, victim);
				razorCount = razorCount - 1;
			}
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierRed", "asw_weapon_normal_armor") > 0)
	{
		if (victim in g_bigKillBuff)
		{
			damageMod = damageMod + 0.8;
		}
	}
	if (RorCountItem(victim.GetMarineName(), "tierRed", "asw_weapon_electrified_armor") > 0)
	{
		if (!(victim.IsElectrifiedArmorActive()) && !(victim in g_ecaCooldown))
		{
			NetProps.SetPropFloat(victim, "m_flElectrifiedArmorEndTime", Time() + 5);
			g_ecaCooldown[victim] <- 50 + 150/RorCountItem(victim.GetMarineName(), "tierRed", "asw_weapon_electrified_armor");
		}
	}
	damage = damage / damageMod;
	if (noDamage)
	{
		return 0;
	}
	return damage;
}

function ProcDanger(victim, attacker, damage, damageType)
{
	if ( !victim || !attacker )
	{
		return;
	}
	if (damage > 0)
	{
		g_inDanger[victim] <- 70;
	}
	if (victim.GetHealth() - damage < victim.GetMaxHealth()*0.25 && victim.GetHealth() > damage)
	{
		if (RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_mines") > 0)
		{
			if (!(victim in g_dangerFireNade))
			{
				victim.EmitSound("weapons/3d/vindicator/fire_grenade.wav");
				local fireNade = DropIncendiaryGrenade(2000.0, 600.0, victim.GetOrigin() + Vector(0, 0, 5));
				fireNade.SetName(victim.GetMarineName());
				g_setDamage[fireNade] <- 2000.0;
				g_dangerFireNade[victim] <- (300/(1+1*RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_mines")));
			}
		}
		if (RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_blink") > 0)
		{
			if (!(victim in g_dangerFreeze))
			{
				victim.EmitSound("weapons/3d/turret/icestop.wav");
				SpawnParticle("freeze_grenade_explosion", victim.GetOrigin());
				SpawnParticle("electrified_armor_burst", victim.GetOrigin() + Vector(0, 0, 50));
				local freezeTarget = null;
				while ((freezeTarget = Entities.FindInSphere(freezeTarget, victim.GetOrigin(), 300+300*RorCountItem(victim.GetMarineName(), "tierWhite", "asw_weapon_laser_mines"))) != null)
				{
					if (freezeTarget.IsAlien() && !(IsInArray(victim, g_friendlyAliens)) && freezeTarget.GetName() != "asw_boss_ror" && CanTrace(victim.GetOrigin(), freezeTarget.GetOrigin()))
					{
						freezeTarget.Freeze(5.0);
					}
				}
				g_dangerFreeze[victim] <- 300/RorCountItem(victim.GetMarineName(), "tierGreen", "asw_weapon_blink");
			}
		}
		if (RorCountItem(victim.GetMarineName(), "tierRed", "asw_weapon_medkit") > 0)
		{
			if (!(victim in g_dangerHeal))
			{
				victim.EmitSound("weapons/3d/meds/kitinject.wav");
				SpawnParticle("heal_recharged", victim.GetOrigin() + Vector(0, 0, 50), 0.1, victim);
				Heal(victim, victim.GetMaxHealth()*0.75);
				g_dangerHeal[victim] <- (300/RorCountItem(victim.GetMarineName(), "tierRed", "asw_weapon_medkit"));
			}
		}
	}
}

function ProcKill(attacker, victim, dropped=false)
{
	if ( !attacker || !victim )
	{
		return;
	}
	local attackerName = "";
	if (attacker.GetClassname() == "asw_marine")
	{
		attackerName = attacker.GetMarineName();
	}
	else if (IsInArray(attacker.GetName(), g_marineNames))
	{
		attackerName = attacker.GetName();
	}
	else
	{
		return;
	}
	local hasDropped = dropped;
	if (!(hasDropped) && RorCountItem(attackerName, "tierWhite", "asw_weapon_medkit") > 0 && Entities.FindByClassnameNearest("asw_weapon_medkit", victim.GetOrigin(), 300.0) == null)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attackerName, "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierWhite", "asw_weapon_medkit"), 0.33))))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierWhite", "asw_weapon_medkit"), 0.33)))
		{
			local medkit = Entities.CreateByClassname("asw_weapon_medkit");
			medkit.SetOrigin(victim.GetOrigin() + Vector(0, 0, 20));
			medkit.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
			medkit.Spawn();
			hasDropped = true;
		}
	}
	if (!(hasDropped) && RorCountItem(attackerName, "tierGreen", "asw_weapon_smart_bomb") > 0 && Entities.FindByClassnameNearest("asw_ammo_drop", victim.GetOrigin(), 300.0) == null)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attackerName, "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierGreen", "asw_weapon_smart_bomb"), 0.33))))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierGreen", "asw_weapon_smart_bomb"), 0.33)))
		{
			local ammo = Entities.CreateByClassname("asw_ammo_drop");
			ammo.SetOrigin(victim.GetOrigin());
			ammo.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
			ammo.Spawn();
			hasDropped = true;
		}
	}
	if (!(hasDropped) && RorCountItem(attackerName, "tierGreen", "asw_weapon_welder") > 0 && Entities.FindByClassnameNearest("asw_weapon_sentry", victim.GetOrigin(), 300.0) == null)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attackerName, "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierGreen", "asw_weapon_welder"), 0.33))))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierGreen", "asw_weapon_welder"), 0.33)))
		{
			local sentry = Entities.CreateByClassname("asw_weapon_sentry");
			sentry.SetOrigin(victim.GetOrigin() + Vector(0, 0, 20));
			sentry.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
			sentry.Spawn();
			hasDropped = true;
		}
	}
	if (RorCountItem(attackerName, "tierWhite", "asw_weapon_mines") > 0)
	{
		local igniteTarget = null;
		while ((igniteTarget = Entities.FindInSphere(igniteTarget, victim.GetOrigin(), 70 + 30*RorCountItem(attackerName, "tierWhite", "asw_weapon_mines"))) != null)
		{
			if (igniteTarget != victim && !(IsInArray(igniteTarget, g_friendlyAliens)) && igniteTarget.IsAlien())
			{
				igniteTarget.Ignite(5.0);
			}
		}
	}
	if (RorCountItem(attackerName, "tierGreen", "asw_weapon_grenades") > 0)
	{
		SpawnParticle("asw_env_explosion", victim.GetOrigin());
		SpawnParticle("explosion_shockwave1", victim.GetOrigin());
		victim.EmitSound(g_bigExplodeSfx[RandomInt(0,g_bigExplodeSfx.len()-1)]);
		local blastTarget = null;
		while ((blastTarget = Entities.FindInSphere(blastTarget, victim.GetOrigin(), 75 + 25*RorCountItem(attackerName, "tierGreen", "asw_weapon_grenades"))) != null)
		{
			if (blastTarget != victim && blastTarget.IsAlien() && CanTrace(victim.GetOrigin(), blastTarget.GetOrigin()))
			{
				blastTarget.TakeDamage(20+20*RorCountItem(attackerName, "tierGreen", "asw_weapon_grenades"), 64, GetMarineByName(attackerName));
			}
		}
	}
	if (RorCountItem(attackerName, "tierRed", "asw_weapon_normal_armor") > 0)
	{
		if (victim in g_elites)
		{
			g_bigKillBuff[GetMarineByName(attackerName)] <- 20 + 40*RorCountItem(attackerName, "tierRed", "asw_weapon_normal_armor");
		}
	}
	if (RorCountItem(attackerName, "tierRed", "asw_weapon_hornet_barrage") > 0)
	{
		local rocketCount = 1;
		if (RorCountItem(attackerName, "tierRed", "asw_weapon_smart_bomb") > 0)
		{
			rocketCount = 3;
		}
		for (local i = 0 ; i < rocketCount ; i++)
		{
			local rocket = Entities.CreateByClassname("asw_rocket");
			rocket.SetOrigin(victim.GetOrigin() + Vector(0, 0, 30));
			rocket.SetAngles(-45.0, RandomFloat(0.0, 360.0), 0.0);
			rocket.Spawn();
			rocket.Activate();
			rocket.SetName(attackerName);
			g_setDamage[rocket] <- 128*RorCountItem(attackerName, "tierRed", "asw_weapon_hornet_barrage") * (0.5+0.5*RorCountItem(attackerName, "tierRed", "asw_weapon_smart_bomb"));
		}
	}
	if (RorCountItem(attackerName, "tierRed", "asw_weapon_stim") > 0)
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attackerName, "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance < (10-10/pow(1+RorCountItem(attackerName, "tierRed", "asw_weapon_stim"), 0.33))))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance < (100-100/pow(1+RorCountItem(attackerName, "tierRed", "asw_weapon_stim"), 0.33)))
		{
			local healBeacon = PlaceHealBeacon(1000, 0, 0.75, 5, 50+50*RorCountItem(attackerName, "tierRed", "asw_weapon_stim"), (victim.GetOrigin() + Vector(0, 0, 20)));
			healBeacon.SetName(attackerName);
		}
	}
	if (victim in g_burnExplode)
	{
		SpawnParticle("asw_env_explosion", victim.GetOrigin());
		SpawnParticle("explosion_shockwave1", victim.GetOrigin());
		victim.EmitSound(g_bigExplodeSfx[RandomInt(0,g_bigExplodeSfx.len()-1)]);
		local blastTarget = null;
		while ((blastTarget = Entities.FindInSphere(blastTarget, victim.GetOrigin(), 150.0)) != null)
		{
			if (blastTarget != victim && blastTarget.IsAlien() && CanTrace(victim.GetOrigin(), blastTarget.GetOrigin()))
			{
				blastTarget.TakeDamage(400*RorCountItem(g_burnExplode[victim].GetMarineName(), "tierRed", "asw_weapon_mines")+blastTarget.GetMaxHealth()*0.15*RorCountItem(g_burnExplode[victim].GetMarineName(), "tierRed", "asw_weapon_mines"), 64, g_burnExplode[victim]);
			}
		}
	}
	if (RorCountItem(attackerName, "tierRed", "asw_weapon_night_vision") > 0 && !(IsInArray(victim.GetClassname(), g_ghostBlacklist)))
	{
		local randomChance = RandomInt(0, 99);
		for (local i = 0 ; i < RorCountItem(attackerName, "tierRed", "asw_weapon_blink") ; i++)
		{
			if (!(randomChance >= 0 && randomChance <= 6))
			{
				randomChance = RandomInt(0, 99);
			}
		}
		if (randomChance >= 0 && randomChance <= 6)
		{
			local alien = Director.SpawnAlienAt(victim.GetClassname(), victim.GetOrigin(), victim.GetAngles());
			if (alien)
			{
				alien.SetModelScale(0.8, 0);
				alien.SetMaxHealth(300*RorCountItem(attackerName, "tierRed", "asw_weapon_night_vision"));
				alien.SetHealth(300*RorCountItem(attackerName, "tierRed", "asw_weapon_night_vision"));
				g_friendlyAliens.append(alien);
				g_ghosts[alien] <- 300*RorCountItem(attackerName, "tierRed", "asw_weapon_night_vision");
				if (victim in g_elites)
				{
					TurnElite(alien, g_elites[victim][0]);
				}
				EntFireByHandle(alien, "Color", "52 201 250", 0, alien, alien);
			}
		}
	}
	if (GetMarineByName(attackerName) != null)
	{
		if (!(GetMarineByName(attackerName) in g_4killsIn1Sec))
		{
			g_4killsIn1Sec[GetMarineByName(attackerName)] <- [];
		}
		g_4killsIn1Sec[GetMarineByName(attackerName)].append(10);
		if (!(GetMarineByName(attackerName) in g_4killsIn7Sec))
		{
			g_4killsIn7Sec[GetMarineByName(attackerName)] <- [];
		}
		g_4killsIn7Sec[GetMarineByName(attackerName)].append(70);
	}
}

function ProcPassive(subject)
{
	if (RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_gas_grenades") > 0)
	{
		if (!(subject in g_inDanger))
		{
			if (subject.GetHealth() < subject.GetMaxHealth())
			{
				SpawnParticle("heal_recharged_glow", subject.GetOrigin() + Vector(0, 0, 20), 0.1, subject);
				Heal(subject, 1*RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_gas_grenades"));
			}
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_freeze_grenades") > 0)
	{
		if (subject.IsInfested())
		{
			SpawnParticle("freeze_grenade_sparkles", subject.GetOrigin());
			local freezeTarget = null;
			while ((freezeTarget = Entities.FindInSphere(freezeTarget, subject.GetOrigin(), 100+50*RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_freeze_grenades"))) != null)
			{
				if (freezeTarget.IsAlien() && !(IsInArray(freezeTarget, g_friendlyAliens)) && freezeTarget.GetName() != "asw_boss_ror")
				{
					freezeTarget.Freeze(2.0);
				}
			}
		}
	}
	if (g_rorTimer % 2 == 0 && RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_fist") > 0)
	{
		local targetList = GetNearestAliens(subject.GetOrigin(), 100.0, RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_fist"));
		foreach (target in targetList)
		{
			if (target.GetHealth() > 0)
			{
				SpawnParticle("lifesteal2", target.GetOrigin() + Vector(0, 0, 20), 1.0, target);
				target.TakeDamage(1, 0, subject);
				if (subject.GetHealth() < subject.GetMaxHealth())
				{
					SpawnParticle("heal_recharged_glow", subject.GetOrigin() + Vector(0, 0, 20), 0.1, subject);
					Heal(subject, 1);
				}
			}
		}
	}
	if (IsSomeoneHacking())
	{
		if (RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_gas_grenades") > 0)
		{
			local marine = null;
			while ((marine = Entities.FindByClassnameWithin(marine, "asw_marine", subject.GetOrigin(), 100.0)) != null)
			{
				if (marine.GetHealth() < marine.GetMaxHealth())
				{
					SpawnParticle("heal_recharged_glow", marine.GetOrigin() + Vector(0, 0, 20), 0.1, marine);
					Heal(marine, 1*RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_gas_grenades"));
				}
			}
		}
		if (RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_flares") > 0)
		{
			if (g_rorTimer % 5 == 0)
			{
				SpawnParticle("vindicator_grenade_flash", subject.GetOrigin());
			}
			local igniteTarget = null;
			while ((igniteTarget = Entities.FindInSphere(igniteTarget, subject.GetOrigin(), 200+100*RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_flares"))) != null)
			{
				if (igniteTarget.IsAlien() && !(IsInArray(igniteTarget, g_friendlyAliens)))
				{
					igniteTarget.Ignite(5.0);
					igniteTarget.ElectroStun(1.0);
				}
			}
		}
		local marine = null;
		local whiteWelderBuff = false;
		while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
		{
			if (RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder") > 0 && (marine.GetOrigin() - subject.GetOrigin()).Length() <= 50 + 50*RorCountItem(marine.GetMarineName(), "tierWhite", "asw_weapon_welder"))
			{
				whiteWelderBuff = true;
			}
		}
		if (whiteWelderBuff)
		{
			SpawnParticle("ad_jump_particle_fountain", subject.GetOrigin() + Vector(0, 0, -50), 0.1, subject);
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_freeze_grenades") > 0)
	{
		SpawnParticle("powerup_freeze_bullets", subject.GetOrigin(), 0.1, subject);
		local damageTarget = null;
		while ((damageTarget = Entities.FindInSphere(damageTarget, subject.GetOrigin(), 200+100*RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_freeze_grenades"))) != null)
		{
			if (damageTarget.IsAlien() && damageTarget.GetHealth() > 0 && NetProps.GetPropFloat(damageTarget, "m_flFrozenTime") - Time() > 0 && !(IsInArray(damageTarget, g_friendlyAliens)))
			{
				SpawnParticle("prifle_grenade_sparks", damageTarget.GetOrigin() + Vector(0, 0, 20), 0.1);
				damageTarget.TakeDamage(50, 0, subject);
			}
		}
	}
	local redHealBeacon = null;
	while ((redHealBeacon = Entities.FindByClassname(redHealBeacon, "asw_healgrenade_projectile")) != null)
	{
		if (IsInArray(redHealBeacon.GetName(), g_marineNames))
		{
			if ((redHealBeacon.GetOrigin() - subject.GetOrigin()).Length() <= 70+50*RorCountItem(redHealBeacon.GetName(), "tierRed", "asw_weapon_stim"))
			{
				Heal(subject, 1);
				subject.CureInfestation();
			}
		}
	}
	if (g_rorTimer % 10 == 0 && RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_tesla_trap") > 0)
	{
		SpawnParticle("electric_field_ground", subject.GetOrigin() + Vector(0, 0, 50), 0.5, subject);
		local stunTarget = null;
		local stunCount = 1+2*RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_tesla_trap");
		while ((stunTarget = Entities.FindInSphere(stunTarget, subject.GetOrigin(), 200.0)) != null)
		{
			if (stunCount > 0 && stunTarget.IsAlien() && stunTarget.GetHealth() > 0 && !(IsInArray(stunTarget, g_friendlyAliens)) && CanTrace(subject.GetOrigin(), stunTarget.GetOrigin()))
			{
				SpawnParticle("electric_field_ground", stunTarget.GetOrigin(), 1.0);
				stunTarget.ElectroStun(5.0);
				stunTarget.TakeDamage(50, 256, subject);
			}
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_gas_grenades") > 0)
	{
		if (subject.GetHealth() < subject.GetMaxHealth()*0.5)
		{
			subject.CureInfestation();
		}
		if (g_rorTimer % 10 == 0)
		{
			Heal(subject, 5*RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_gas_grenades"));
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_laser_mines") > 0)
	{
		if (!(subject in g_inDanger))
		{
			SpawnParticle("grenade_smoke", subject.GetOrigin() + Vector(0, 0, 50), 0.1, subject);
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_electrified_armor") > 0)
	{
		if (!(subject in g_inDanger))
		{
			SpawnParticle("electric_weapon_zap_muzzle_off", subject.GetOrigin() + Vector(0, 0, 50), 0.1, subject);
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierWhite", "asw_weapon_fist") > 0)
	{
		SpawnParticle("buffgrenade_pulse", subject.GetOrigin(), 0.1, subject);
	}
	if (RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_normal_armor") > 0)
	{
		if (subject in g_frenzy)
		{
			SpawnParticle("ad_jump_particle", subject.GetOrigin(), 0.1, subject);
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierGreen", "asw_weapon_jump_jet") > 0)
	{
		if (!(subject in g_inDanger))
		{
			SpawnParticle("ad_eggs_particle_fountain_red", subject.GetOrigin() + Vector(0, 0, -50), 0.1, subject);
		}
	}
	if (RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_normal_armor") > 0)
	{
		if (subject in g_bigKillBuff)
		{
			SpawnParticle("ad_core_tailsfire", subject.GetOrigin(), 1.0, subject);
		}
	}
}

function Heal(subject, healAmt, extinguish=true)
{
	if (!(subject.IsValid()) || subject.GetHealth() <= 0 || subject in g_noHealing)
	{
		return;
	}
	if (extinguish)
	{
		subject.Extinguish();
	}
	if (subject.GetHealth() + healAmt > subject.GetMaxHealth())
	{
		subject.SetHealth(subject.GetMaxHealth());
	}
	else
	{
		subject.SetHealth(subject.GetHealth() + healAmt);
	}
}

function QueueRocket(subject, damage, count=1)
{
	if (RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_smart_bomb") > 0)
	{
		count = count * 3;
		damage = damage * (0.5+0.5*RorCountItem(subject.GetMarineName(), "tierRed", "asw_weapon_smart_bomb"));
	}
	if (!(subject in g_rocketQueue))
	{
		g_rocketQueue[subject] <- [];
	}
	for (local i = 0 ; i < count ; i++)
	{
		g_rocketQueue[subject].append(damage);
	}
}

function RorDropItem(origin, tier=null, name=null, count=1)
{
	for (local i = 0 ; i < count ; i++)
	{
		local itemTier = tier;
		if (itemTier == null)
		{
			local randomChance = RandomInt(0, 90);
			if (randomChance >= 0 && randomChance <= 62)
			{
				itemTier = "tierWhite";
			}
			else if (randomChance >= 63 && randomChance <= 89)
			{
				itemTier = "tierGreen";
			}
			else if (randomChance == 90)
			{
				itemTier = "tierRed";
			}
		}
		local itemName = name;
		if (name == null)
		{
			itemName = g_offhandItems[RandomInt(0, g_offhandItems.len() - 1)];
		}
		local itemPickup = Entities.CreateByClassname(itemName);
		itemPickup.SetOrigin(origin);
		if (itemName == "asw_weapon_welder")
		{
			itemPickup.SetAngles(90.0, RandomFloat(0.0, 360.0), 0.0);
		}
		else
		{
			itemPickup.SetAngles(0.0, RandomFloat(0.0, 360.0), 0.0);
		}
		itemPickup.__KeyValueFromInt( "IsTemporaryPickup", 1 );
		itemPickup.Spawn();
		if (itemPickup.Clip1() > 0)
		{
			itemPickup.SetClip1(-1);
		}
		if (itemName == "asw_weapon_blink")
		{
			NetProps.SetPropFloat( itemPickup, "m_flPower", -99999.0 );
		}
		for (local i = 0 ; i < 100 ; i++)
		{
			itemPickup.EmitSound("weapons/ar2/ar2_reload_push.wav");
		}
		if (itemTier == "tierRed")
		{
			for (local i = 0 ; i < 10 ; i++)
			{
				itemPickup.EmitSound("weapons/physcannon/energy_sing_explosion2.wav");
			}
		}
		local hSprite = Entities.CreateByClassname( "env_sprite" );
		hSprite.__KeyValueFromInt( "spawnflags", 1 );
		hSprite.__KeyValueFromString( "Model", "materials/Sprites/light_glow03.vmt" );
		if (itemTier == "tierWhite")
		{
			hSprite.__KeyValueFromString( "rendercolor", "255 255 255" );
		}
		else if (itemTier == "tierGreen")
		{
			hSprite.__KeyValueFromString( "rendercolor", "0 255 0" );
		}
		else if (itemTier == "tierRed")
		{
			hSprite.__KeyValueFromString( "rendercolor", "255 0 0" );
		}
		hSprite.__KeyValueFromFloat( "GlowProxySize", 24 );
		hSprite.__KeyValueFromInt( "renderamt", 192 );
		hSprite.__KeyValueFromInt( "rendermode", 9 );
		hSprite.__KeyValueFromFloat( "scale", 1.0 );
		hSprite.SetOrigin( itemPickup.GetOrigin() );
		hSprite.SetParent( itemPickup );
		hSprite.Spawn();
		hSprite.Activate();
		g_rorItemPickups[itemPickup] <- [itemTier, itemPickup.GetOrigin()];
		while (!(IsOnGround(itemPickup)))
		{
			g_rorItemPickups[itemPickup][1] = g_rorItemPickups[itemPickup][1] + Vector(0, 0, -1);
			itemPickup.SetOrigin(g_rorItemPickups[itemPickup][1]);
		}
		if (count == 1)
		{
			return itemPickup;
		}
	}
}

function TurnElite(subject, type=null)
{
	if (subject in g_elites)
	{
		return subject;
	}
	local eliteType = "";
	local eliteTier = 0;
	if (type == null)
	{
		local randomChance = RandomInt(0, (350/g_carnageNumber).tointeger() + 1);
		if (g_rorStage > 5 && randomChance == 0)
		{
			eliteType = g_eliteTypes2[RandomInt(0, g_eliteTypes2.len()-1)];
		}
		else if (randomChance >= 1 && randomChance <= 6)
		{
			eliteType = g_eliteTypes1[RandomInt(0, g_eliteTypes1.len()-1)];
		}
		else
		{
			return subject;
		}
	}
	else
	{
		eliteType = type;
	}
	if (IsInArray(eliteType, g_eliteTypes1))
	{
		eliteTier = 1;
		local newHealth = subject.GetMaxHealth()*4;
		subject.SetMaxHealth(newHealth);
		subject.SetHealth(newHealth);
		if (eliteType == "typeFire")
		{
			EntFireByHandle(subject, "Color", "250 52 5", 0, subject, subject);
		}
		else if (eliteType == "typeLightning")
		{
			EntFireByHandle(subject, "Color", "5 52 250", 0, subject, subject);
		}
		else if (eliteType == "typeEarth")
		{
			EntFireByHandle(subject, "Color", "52 250 5", 0, subject, subject);
		}
	}
	else if (IsInArray(eliteType, g_eliteTypes2))
	{
		eliteTier = 2;
		local newHealth = subject.GetMaxHealth()*18;
		subject.SetMaxHealth(newHealth);
		subject.SetHealth(newHealth);
		if (eliteType == "typeCorruption")
		{
			EntFireByHandle(subject, "Color", "5 52 5", 0, subject, subject);
		}
		else if (eliteType == "typeIncorporeality")
		{
			EntFireByHandle(subject, "Color", "103 201 201", 0, subject, subject);
		}
	}
	g_elites[subject] <- [eliteType, eliteTier];
	return subject;
}

function UndoElite(subject)
{
	if (!(subject in g_elites))
	{
		return subject;
	}
	if (g_elites[subject][1] == 1)
	{
		local newHealth = subject.GetMaxHealth()/4;
		subject.SetMaxHealth(newHealth);
		subject.SetHealth(newHealth);
	}
	else if (g_elites[subject][1] == 2)
	{
		local newHealth = subject.GetMaxHealth()/18;
		subject.SetMaxHealth(newHealth);
		subject.SetHealth(newHealth);
	}
	g_elites.rawdelete(subject);
	return subject;
}

function UnitToTime(unit, countHour=false, decimal=false, unitPerSecond=10)
{
	local time = unit/unitPerSecond;
	local sec = time%60;
	if (!(decimal))
	{
		sec = sec.tointeger();
	}
	time = time - time%60;
	if (time <= 0)
	{
		return [0, 0, sec];
	}
	if (countHour)
	{
		local min = time%3600/60;
		time = time - time%3600;
		if (time <= 0)
		{
			return [0, min, sec];
		}
		local hour = time/3600;
		return [hour, min, sec];
	}
	local min = time/60;
	return [0, min, sec];
}

function GetMarineByName(name)
{
	local marine = null;
	while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
	{
		if (marine.GetMarineName() == name)
		{
			return marine;
		}
	}
	return null;
}

function GetDiffText(translation=true, skill=null)
{
	if (skill == null)
	{
		skill = g_rorDiff;
	}
	if (translation)
	{
		if ( skill == 1 ) { // easy
			return "#asw_difficulty_easy";
		} else if ( skill == 2 ) { // normal
			return "#asw_difficulty_normal";
		} else if ( skill == 3 ) { // hard
			return "#asw_difficulty_hard";
		} else if ( skill == 4 ) { // insane
			return "#asw_difficulty_insane";
		} else if ( skill == 5 ) { // brutal
			return "#asw_difficulty_imba";
		}
	}
	else
	{
		if ( skill == 1 ) { // easy
			return "Easy";
		} else if ( skill == 2 ) { // normal
			return "Normal";
		} else if ( skill == 3 ) { // hard
			return "Hard";
		} else if ( skill == 4 ) { // insane
			return "Insane";
		} else if ( skill == 5 ) { // brutal
			return "Brutal";
		}
	}
	return Convars.GetFloat("asw_skill").tostring();
}

function RorInit()
{
	local marine = null;
	while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
	{
		g_activeMarines.append(marine.GetMarineName());
	}
	if (FileToString("challenge_risk_of_rain_save") != "")
	{
		local progress = split(FileToString("challenge_risk_of_rain_save"), "|");
		g_rorDiff = progress[0].tointeger();
		if (Convars.GetFloat( "asw_skill" ) != g_rorDiff)
		{
			ClientPrint(null, 3, "#ror_diffNotMatch");
			FixDifficulty();
			NetProps.SetPropInt(asw_gamerules, "m_iSkillLevel", g_rorDiff);
			Convars.SetValue( "asw_skill", g_rorDiff );
		}
		g_playerCount = progress[1].tointeger();
		if (g_playerCount < g_activeMarines.len())
		{
			g_playerCount = g_activeMarines.len();
		}
		g_rorTimer = progress[2].tointeger();
		g_rorStage = progress[3].tointeger() + 1;
		g_rorMarineExp = progress[4].tointeger();
		g_totalKills = progress[5].tointeger();
		g_spawnBoss = progress[6].tointeger();
		ClientPrint(null, 3, "#ror_progressReport_load", TimeReport(), g_rorStage.tostring(), GetDiffText());
	}
	g_playedMaps.append(GetMapName().tolower());
	if (g_playerCount <= 0)
	{
		g_playerCount = g_activeMarines.len();
	}
	foreach (name in g_marineNames)
	{
		if (FileToString("challenge_risk_of_rain_inventory_" + name) != "")
		{
			local data = split(FileToString("challenge_risk_of_rain_inventory_" + name), "|");
			// 将英文名映射到实际的角色名
			local actualMarineName = name;
			foreach (activeName in g_activeMarines)
			{
				if ((name == "Vegas" && activeName == "赌徒") ||
					(name == "Jaeger" && activeName == "杰格") ||
					(name == "Wolfe" && activeName == "乌尔夫") ||
					(name == "Faith" && activeName == "白莲") ||
					(name == "Crash" && activeName == "溃客") ||
					(name == "Sarge" && activeName == "上尉") ||
					(name == "Wildcat" && activeName == "野猫") ||
					(name == activeName))
				{
					actualMarineName = activeName;
					break;
				}
			}
			while (data.len() > 0)
			{
				local tier = data.remove(0);
				local item = data.remove(0);
				local count = data.remove(0).tointeger();
				RorAddItem(actualMarineName, tier, item, count);
			}
			ClientPrint(null, 2, "#ror_loadedInv", "#asw_name_" + actualMarineName);
		}
	}
	foreach (item, value in g_inventories)
	{
		if (!(IsInArray(item, g_activeMarines)))
		{
			ClientPrint(null, 3, "#ror_deleteInv", "#asw_name_" + item);
			g_inventories.rawdelete(item);
		}
	}
	foreach (name in g_marineNames)
	{
		if (FileToString("challenge_risk_of_rain_inventory_" + name) == "" && g_rorStage >= 5 && IsInArray(name, g_activeMarines))
		{
			local count = (g_rorStage*1.3).tointeger();
			for (local i = 0 ; i < count ; i++)
			{
				local itemTier = "tierWhite";
				local randomChance = RandomInt(0, 89);
				if (randomChance >= 63 && randomChance <= 89)
				{
					itemTier = "tierGreen";
				}
				local itemName = g_offhandItems[RandomInt(2, g_offhandItems.len() - 1)];
				RorAddItem(name, itemTier, itemName);
			}
			local player = null;
			while((player = Entities.FindByClassname(player, "player")) != null)
			{
				if (player == GetMarineByName(name).GetCommander())
				{
					ClientPrint(player, 3, "#ror_outlierAssist", COLOR_TEXT, COLOR_YELLOW + count.tostring() + COLOR_TEXT);
				}
				else
				{
					ClientPrint(player, 3, "#ror_outlierAssistOther", COLOR_GREY, GetMarineByName(name).GetCommander().GetPlayerName(), COLOR_YELLOW + count.tostring() + COLOR_GREY);
				}
			}
		}
	}
	if (g_rorStage%5 == 0)
	{
		local player_start = Entities.FindByClassname(0, "info_player_start");
		local player_start_pos = Vector( 0, 0, 0 );
		if (player_start)
		{
			player_start_pos = player_start.GetOrigin()
		}
		for( local i = 0; i < g_rorStage/5; i++ )
		{
			for( local i = 0; i < 50; i++ )
			{
				local node_id = RandomInt( 0, InfoNodes.GetNumNodes() - 1 );
				local node_type = InfoNodes.GetNodeType( node_id );
				if ( node_type != NODE_GROUND )
				{
					continue;
				}
				local vecPos = InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ) - player_start_pos;
				if (vecPos.LengthSqr() > 1000000 && Director.ValidSpawnPoint(InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ), Vector(-40,-40, 0), Vector(40, 40, 72)) )
				{
					local queen = Director.SpawnAlienAt( "asw_queen", InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ) + Vector(0, 0, 20), Vector( 0, 0, 0 ) );
					if (queen)
					{
						queen.SetName("asw_queen_ror");
						g_queenCount = g_queenCount + 1;
						break;
					}
				}
			}
		}
		if (g_queenCount > 0)
		{
			g_keyObj = Entities.CreateByClassname("asw_objective_triggered");
			g_keyObj.__KeyValueFromString("objectivetitle", "#ror_keyObj");
			g_keyObj.__KeyValueFromString("objectivedescription1", "#ror_keyObjDesc");
			g_keyObj.__KeyValueFromInt("Optional", 1);
			g_keyObj.__KeyValueFromInt("Visible", 0);
			g_keyObj.Spawn();
			g_queenObj = Entities.CreateByClassname("asw_objective_triggered");
			g_queenObj.__KeyValueFromString("objectivetitle", "#ror_queenObj");
			g_queenObj.__KeyValueFromString("objectivedescription1", "#ror_queenObjDesc");
			g_queenObj.__KeyValueFromInt("SetMaxProgress", g_queenCount);
			g_queenObj.__KeyValueFromInt("Optional", 1);
			g_queenObj.Spawn();
		}
	}
	if (g_spawnBoss > 0)
	{
		local player_start = Entities.FindByClassname(0, "info_player_start");
		local player_start_pos = Vector( 0, 0, 0 );
		if (player_start)
		{
			player_start_pos = player_start.GetOrigin()
		}
		for( local i = 0; i < 50; i++ )
		{
			local node_id = RandomInt( 0, InfoNodes.GetNumNodes() - 1 );
			local node_type = InfoNodes.GetNodeType( node_id );
			if ( node_type != NODE_GROUND )
			{
				continue;
			}
			local vecPos = InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ) - player_start_pos;
			if (vecPos.LengthSqr() > 1000000 && Director.ValidSpawnPoint(InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ), Vector(-40,-40, 0), Vector(40, 40, 72)) )
			{
				local boss = Director.SpawnAlienAt( "asw_drone_uber", InfoNodes.GetNodePosition( node_id, HULL_MEDIUMBIG ) + Vector(0, 0, 20), Vector( 0, 0, 0 ) );
				if (boss)
				{
					g_spawnBoss = 0;
					g_bossSpawned = true;
					UndoElite(boss);
					boss.SetName("asw_boss_ror");
					boss.SetModelScale(2.0, 0);
					boss.__KeyValueFromInt( "SpeedScale", 3.0 );
					NetProps.SetPropBool(boss, "m_bFreezable", false);
					NetProps.SetPropBool(boss, "m_bFlinches", false);
					EntFireByHandle(boss, "Color", "255 0 0", 0, boss, boss);
					boss.SetMaxHealth(10000+GetAlienLevel()*3000);
					boss.SetHealth(10000+GetAlienLevel()*3000);
					ClientPrint(null, 3, "#ror_bossSpawned", COLOR_RED);
					break;
				}
			}
		}
		local bossEnt = null;
		bossEnt = Entities.FindByName(bossEnt, "asw_boss_ror");
		if (bossEnt != null)
		{
			g_bossObj = Entities.CreateByClassname("asw_objective_triggered");
			g_bossObj.__KeyValueFromString("objectivetitle", "#ror_bossObj");
			g_bossObj.__KeyValueFromString("objectivedescription1", "#ror_bossObjDesc");
			g_bossObj.__KeyValueFromInt("Optional", 1);
			g_bossObj.Spawn();
		}
		else
		{
			g_spawnBoss = 1;
			ClientPrint(null, 3, "#ror_bossSpawnFail");
		}
	}
	if (!(g_bossSpawned))
	{
		g_carnageNumber = ((g_rorStage-1)/5+1).tointeger();
		Convars.SetValue("rd_carnage_scale", g_carnageNumber);
		Convars.ExecuteConCommand( "asw_carnage " + g_carnageNumber.tostring() );
		ClientPrint(null, 3, "#ror_currentCarnage", COLOR_TEXT, COLOR_YELLOW + g_carnageNumber.tostring());
	}
}

function RorSave()
{
	StringToFile("challenge_risk_of_rain_resetSwitch", "");
	if (g_bossKey != null && g_bossKey.GetMoveParent() != null && g_bossKey.GetMoveParent().GetClassname() == "asw_marine")
	{
		EntFireByHandle( g_keyObj, "SetComplete", "", 0.0, null, null );
		g_spawnBoss = 1;
	}
	local data = g_rorDiff.tostring() + "|" + g_playerCount.tostring() + "|" + g_rorTimer.tostring() + "|" + g_rorStage.tostring() + "|" + g_rorMarineExp.tostring() + "|" + g_totalKills.tostring() + "|" + g_spawnBoss.tostring();
	StringToFile("challenge_risk_of_rain_save", data);
	data = "";
	while (g_playedMaps.len() > 4)
	{
		g_playedMaps.remove(0);
	}
	foreach (item in g_playedMaps)
	{
		data = data + item + "|";
	}
	StringToFile("challenge_risk_of_rain_playedMaps", data);
	// 先清空所有可能的背包文件
	foreach (name in g_marineNames)
	{
		StringToFile("challenge_risk_of_rain_inventory_" + name, "");
	}
	// 然后保存实际存在的背包数据
	foreach (marineName, inventory in g_inventories)
	{
		local itemData = "";
		foreach (tier, itemList in inventory)
		{
			foreach (item, count in itemList)
			{
				itemData = itemData + tier + "|" + item + "|" + count.tostring() + "|";
			}
		}
		// 尝试将角色名映射到英文名称进行保存
		local saveFileName = marineName;
		if (marineName == "赌徒") saveFileName = "Vegas";
		else if (marineName == "杰格") saveFileName = "Jaeger";
		else if (marineName == "乌尔夫") saveFileName = "Wolfe";
		else if (marineName == "白莲") saveFileName = "Faith";
		else if (marineName == "溃客") saveFileName = "Crash";
		else if (marineName == "上尉") saveFileName = "Sarge";
		else if (marineName == "野猫") saveFileName = "Wildcat";
		StringToFile("challenge_risk_of_rain_inventory_" + saveFileName, itemData);
	}
	foreach (name in g_activeMarines)
	{
		InspectInventory(null, name);
	}
	ClientPrint(null, 3, "#ror_everyInvListed");
	ClientPrint(null, 3, "#ror_progressReport_save", TimeReport(), (g_rorStage+1).tostring(), GetDiffText());
	if (g_spawnBoss == 1)
	{
		ClientPrint(null, 3, "#ror_bossWillSpawn");
	}
}

function RorReset()
{
	StringToFile("challenge_risk_of_rain_resetSwitch", "");
	StringToFile("challenge_risk_of_rain_save", "");
	StringToFile("challenge_risk_of_rain_playedMaps", "");
	foreach (name in g_marineNames)
	{
		StringToFile("challenge_risk_of_rain_inventory_" + name, "");
	}
}

function RorResetKeepInventory()
{
	StringToFile("challenge_risk_of_rain_resetSwitch", "");
	// 保存上一关的进度，但Stage回退到上一关
	local previousStage = g_rorStage - 1;
	if (previousStage < 1) previousStage = 1;
	local data = g_rorDiff.tostring() + "|" + g_playerCount.tostring() + "|" + g_rorTimer.tostring() + "|" + previousStage.tostring() + "|" + g_rorMarineExp.tostring() + "|" + g_totalKills.tostring() + "|" + "0";
	StringToFile("challenge_risk_of_rain_save", data);
	StringToFile("challenge_risk_of_rain_playedMaps", "");
	// 保存当前背包数据到文件
	// 先清空所有可能的背包文件
	foreach (name in g_marineNames)
	{
		StringToFile("challenge_risk_of_rain_inventory_" + name, "");
	}
	// 然后保存实际存在的背包数据
	foreach (marineName, inventory in g_inventories)
	{
		local itemData = "";
		foreach (tier, itemList in inventory)
		{
			foreach (item, count in itemList)
			{
				itemData = itemData + tier + "|" + item + "|" + count.tostring() + "|";
			}
		}
		// 尝试将角色名映射到英文名称进行保存
		local saveFileName = marineName;
		if (marineName == "赌徒") saveFileName = "Vegas";
		else if (marineName == "杰格") saveFileName = "Jaeger";
		else if (marineName == "乌尔夫") saveFileName = "Wolfe";
		else if (marineName == "白莲") saveFileName = "Faith";
		else if (marineName == "溃客") saveFileName = "Crash";
		else if (marineName == "上尉") saveFileName = "Sarge";
		else if (marineName == "野猫") saveFileName = "Wildcat";
		StringToFile("challenge_risk_of_rain_inventory_" + saveFileName, itemData);
	}
}

function RorAddItem(subject, tier, item, count=1)
{
	if (!(subject in g_inventories))
	{
		g_inventories[subject] <- {};
	}
	if (!(tier in g_inventories[subject]))
	{
		g_inventories[subject][tier] <- {};
	}
	if (item in g_inventories[subject][tier])
	{
		g_inventories[subject][tier][item] = g_inventories[subject][tier][item] + count;
	}
	else
	{
		g_inventories[subject][tier][item] <- count;
	}
}

function RorCountItem(subject, tier, item)
{
	if (!(subject in g_inventories))
	{
		return 0;
	}
	if (!(tier in g_inventories[subject]))
	{
		return 0;
	}
	if (item in g_inventories[subject][tier])
	{
		return g_inventories[subject][tier][item];
	}
	return 0;
}

function InspectInventory(player=null, targetMarine=null)
{
	local marineName = "";
	if (targetMarine == null && player != null)
	{
		local marine = player.GetViewNPC();
		if (marine == null)
		{
			return;
		}
		marineName = marine.GetMarineName();
	}
	else if (targetMarine == null && player == null)
	{
		return;
	}
	else
	{
		marineName = targetMarine;
	}
	ClientPrint(player, 2, " ");
	ClientPrint(player, 3, "#ror_invDivider", COLOR_TEXT, "#asw_name_" + marineName);
	if (!(marineName in g_inventories))
	{
		ClientPrint(player, 3, "#ror_noItems");
	}
	else
	{
		if ("tierRed" in g_inventories[marineName])
		{
			foreach (item, count in g_inventories[marineName]["tierRed"])
			{
				ClientPrint(player, 3, "#ror_invItem", COLOR_RED + "Legendary " + ClassnameToName(item) + " ×" + RorCountItem(marineName, "tierRed", item).tostring() + COLOR_TEXT, RorItemDesc("tierRed", item));
			}
		}
		if ("tierGreen" in g_inventories[marineName])
		{
			foreach (item, count in g_inventories[marineName]["tierGreen"])
			{
				ClientPrint(player, 3, "#ror_invItem", COLOR_GREEN + "Uncommon " + ClassnameToName(item) + " ×" + RorCountItem(marineName, "tierGreen", item).tostring() + COLOR_TEXT, RorItemDesc("tierGreen", item));
			}
		}
		if ("tierWhite" in g_inventories[marineName])
		{
			foreach (item, count in g_inventories[marineName]["tierWhite"])
			{
				ClientPrint(player, 3, "#ror_invItem", COLOR_WHITE + "Common " + ClassnameToName(item) + " ×" + RorCountItem(marineName, "tierWhite", item).tostring() + COLOR_TEXT, RorItemDesc("tierWhite", item));
			}
		}
	}
	ClientPrint(player, 3, "#ror_invDivider", COLOR_TEXT, "#asw_name_" + marineName);
	if (marineName in g_inventories)
	{
		ClientPrint(player, 3, "#ror_seeConsole");
	}
	ClientPrint(player, 2, " ");
}

function TimeReport()
{
	local timeText = "";
	if (UnitToTime(g_rorTimer, true)[0] > 0)
	{
		timeText = timeText + UnitToTime(g_rorTimer, true)[0].tostring() + "h";
	}
	if (UnitToTime(g_rorTimer, true)[1] > 0)
	{
		timeText = timeText + UnitToTime(g_rorTimer, true)[1].tostring() + "m";
	}
	if (UnitToTime(g_rorTimer, true)[2] > 0)
	{
		timeText = timeText + UnitToTime(g_rorTimer, true)[2].tostring() + "s";
	}
	if (UnitToTime(g_rorTimer, true)[0] <= 0 && UnitToTime(g_rorTimer, true)[1] <= 0 && UnitToTime(g_rorTimer, true)[2] <= 0)
	{
		timeText = timeText + UnitToTime(g_rorTimer, true)[2].tostring() + "s";
	}
	return timeText;
}

function BossAttack(attacker, origin, target=null)
{
	if (attacker.GetHealth() <= 0)
	{
		return;
	}
	SpawnParticle1("ad_eggs_particle_pulse_red", origin, 2.0, null, false, false);
	SpawnParticle1("melee_charged", origin, 2.0, null, false, false);
	if (target != null)
	{
		target.EmitSound("explosions/teleport_blink.wav");
	}
	attacker.EmitSound("explosions/teleport_blink.wav");
	g_bossAttackTracker[origin] <- 20;
}

function GetPlayerFactor()
{
	return 1 + 0.3 * (g_playerCount - 1);
}

function GetTimeFactor()
{
	return 0.0506 * g_rorDiff * pow(g_playerCount, 0.2);
}

function GetStageFactor()
{
	return pow(1.15, (g_rorStage - 1));
}

function GetCoeff()
{
	return (GetPlayerFactor() + UnitToTime(g_rorTimer)[1] * GetTimeFactor()) * GetStageFactor();
}

function GetAlienLevel()
{
	return 1 + ((GetCoeff() - GetPlayerFactor()) / 0.33).tointeger();
}

function GetMarineLevel()
{
	return (log(1 + 0.0275 * g_rorMarineExp)/log(1.55)).tointeger() + 1;
}

function FixDifficulty()
{
	local skill = Convars.GetFloat("asw_skill");
	local current = skill;
	if (skill < g_rorDiff)
	{
		while (current < g_rorDiff)
		{
			if (current == 1)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "2");
				current = current + 1;
			}
			else if (current == 2)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "2");
				current = current + 1;
			}
			else if (current == 3)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "3");
				current = current + 1;
			}
			else if (current == 4)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "3");
				current = current + 1;
			}
			else
			{
				return;
			}
		}
	}
	else
	{
		while (current > g_rorDiff)
		{
			if (current == 5)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "-3");
				current = current - 1;
			}
			else if (current == 4)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "-3");
				current = current - 1;
			}
			else if (current == 3)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "-2");
				current = current - 1;
			}
			else if (current == 2)
			{
				EntFire("asw_gamerules", "ModifyDifficulty", "-2");
				current = current - 1;
			}
			else
			{
				return;
			}
		}
	}
}

function IsSomeoneHacking()
{
	local marine = null;
	while ((marine = Entities.FindByClassname(marine, "asw_marine")) != null)
	{
		local hUsing = NetProps.GetPropEntity( marine, "m_hUsingEntity" );
		if ( hUsing != null && (hUsing.GetClassname() == "trigger_asw_button_area" || hUsing.GetClassname() == "trigger_asw_computer_area") && hUsing.GetKeyValue( "locked" ) == "1" )
		{
			return true;
		}
	}
	return false;
}

function GetNearestAliens(origin, radius, count=1)
{
	local resultArray = [];
	for (local i = 0 ; i < count ; i++)
	{
		local target = null;
		local find = null;
		while ((find = Entities.FindInSphere(find, origin, radius)) != null)
		{
			if (find.IsAlien() && !(IsInArray(find, resultArray)) && find.GetHealth() > 0 && !(IsInArray(find, g_friendlyAliens)) && (target == null || (target != null && (target.GetOrigin() - origin).Length() > (find.GetOrigin() - origin).Length())))
			{
				target = find;
			}
		}
		if (target != null)
		{
			resultArray.append(target);
		}
	}
	return resultArray;
}

function GetNearestAlien(origin, radius)
{
	local target = null;
	local find = null;
	while ((find = Entities.FindInSphere(find, origin, radius)) != null)
	{
		if (find.IsAlien() && find.GetHealth() > 0 && !(IsInArray(find, g_friendlyAliens)) && (target == null || (target != null && (target.GetOrigin() - origin).Length() > (find.GetOrigin() - origin).Length())))
		{
			target = find;
		}
	}
	return target;
}

function CanTrace(origin1, origin2, ignore=null)
{
	if (TraceLine(origin1, origin2, ignore) == 1)
	{
		return true;
	}
	return false;
}

function IsOnGround(ent)
{
	if (TraceLine(ent.GetOrigin(), ent.GetOrigin() + Vector(0, 0, -1), null) == 1)
	{
		return false;
	}
	return true;
}

function SpawnParticle(name, origin, delay=5.0, attach=null, visible=true, limit=true)
{
	if (limit && g_particle_amt_0 >= 100)
	{
		return;
	}
	if (visible && Entities.FindByClassnameNearest("asw_marine", origin, 900.0) == null)
	{
		return;
	}
	if (Entities.FindByName(null, "particles_" + g_particle_count_0.tostring()) != null)
	{
		return;
	}
	local hParticles = Entities.CreateByClassname( "info_particle_system" );
	local strParticleName = "particles_" + g_particle_count_0.tostring();
	if (g_particle_count_0 >= 199)
	{
		g_particle_count_0 = 0;
	}
	else
	{
		g_particle_count_0 = g_particle_count_0 + 1;
	}
	hParticles.SetName( strParticleName );
	hParticles.__KeyValueFromInt( "start_active", 1 );
	hParticles.__KeyValueFromString( "effect_name", name );
	hParticles.SetOrigin( origin );
	if (parent != null)
	{
		hParticles.SetParent( attach );
	}
	hParticles.Spawn();
	hParticles.Activate();
	DelayCodeExecution( "try{Entities.FindByName( null, " + "\"" + strParticleName + "\"" + " ).Destroy()}catch(error){}", delay );
	DelayCodeExecution( "g_particle_amt_0 = g_particle_amt_0 - 1", delay );
}
function SpawnParticle1(name, origin, delay=5.0, attach=null, visible=true, limit=true)
{
	if (limit && g_particle_amt_1 >= 100)
	{
		return;
	}
	if (visible && Entities.FindByClassnameNearest("asw_marine", origin, 900.0) == null)
	{
		return;
	}
	if (Entities.FindByName(null, "particles1_" + g_particle_count_1.tostring()) != null)
	{
		return;
	}
	local hParticles = Entities.CreateByClassname( "info_particle_system" );
	local strParticleName = "particles1_" + g_particle_count_1.tostring();
	if (g_particle_count_1 >= 199)
	{
		g_particle_count_1 = 0;
	}
	else
	{
		g_particle_count_1 = g_particle_count_1 + 1;
	}
	hParticles.SetName( strParticleName );
	hParticles.__KeyValueFromInt( "start_active", 1 );
	hParticles.__KeyValueFromString( "effect_name", name );
	hParticles.SetOrigin( origin );
	if (parent != null)
	{
		hParticles.SetParent( attach );
	}
	hParticles.Spawn();
	hParticles.Activate();
	DelayCodeExecution( "try{Entities.FindByName( null, " + "\"" + strParticleName + "\"" + " ).Destroy()}catch(error){}", delay );
	DelayCodeExecution( "g_particle_amt_1 = g_particle_amt_1 - 1", delay );
}
foreach (name in g_particleLibrary)
{
	SpawnParticle(name, info_target_precache.GetOrigin() + Vector(0, 0, 15000), 5.0, null, false);
}

function CelestineNearby(origin, radius=200.0)
{
	local check = null;
	while ((check = Entities.FindInSphere(check, origin, radius)) != null)
	{
		if (check in g_elites && g_elites[check][0] == "typeIncorporeality" && check.GetHealth() > 0 && !(IsInArray(check, g_friendlyAliens)))
		{
			return true;
		}
	}
	return false;
}

function ClassnameToName(name)
{
	if (name == "asw_weapon_medkit")
	{
		return "Healing Kit";
	}
	else if (name == "asw_weapon_welder")
	{
		return "Welder";
	}
	else if (name == "asw_weapon_flares")
	{
		return "Flares";
	}
	else if (name == "asw_weapon_laser_mines")
	{
		return "Trip Mines";
	}
	else if (name == "asw_weapon_normal_armor")
	{
		return "Heavy Armor";
	}
	else if (name == "asw_weapon_buff_grenade")
	{
		return "Damage Amplifier";
	}
	else if (name == "asw_weapon_hornet_barrage")
	{
		return "Hornet Barrage";
	}
	else if (name == "asw_weapon_freeze_grenades")
	{
		return "Freeze Grenades";
	}
	else if (name == "asw_weapon_stim")
	{
		return "Adrenaline";
	}
	else if (name == "asw_weapon_tesla_trap")
	{
		return "Tesla Sentry Coil";
	}
	else if (name == "asw_weapon_electrified_armor")
	{
		return "Electric Armor";
	}
	else if (name == "asw_weapon_mines")
	{
		return "Incendiary Mines";
	}
	else if (name == "asw_weapon_fist")
	{
		return "Power Fist";
	}
	else if (name == "asw_weapon_grenades")
	{
		return "Hand Grenades";
	}
	else if (name == "asw_weapon_night_vision")
	{
		return "Nightvision Goggles";
	}
	else if (name == "asw_weapon_smart_bomb")
	{
		return "Smart Bomb";
	}
	else if (name == "asw_weapon_gas_grenades")
	{
		return "Gas Grenades";
	}
	else if (name == "asw_weapon_blink")
	{
		return "Blink";
	}
	else if (name == "asw_weapon_jump_jet")
	{
		return "Assault Jets";
	}
	else
	{
		return name;
	}
}

function RorItemDesc(tier, name, translation=true)
{
	if (translation)
	{
		if (tier == "tierWhite")
		{
			if (name == "asw_weapon_medkit")
			{
				return "#ror_itemDesc_white_medkit";
			}
			else if (name == "asw_weapon_welder")
			{
				return "#ror_itemDesc_white_welder";
			}
			else if (name == "asw_weapon_flares")
			{
				return "#ror_itemDesc_white_flares";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "#ror_itemDesc_white_laser_mines";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "#ror_itemDesc_white_normal_armor";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "#ror_itemDesc_white_buff_grenade";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "#ror_itemDesc_white_hornet_barrage";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "#ror_itemDesc_white_freeze_grenades";
			}
			else if (name == "asw_weapon_stim")
			{
				return "#ror_itemDesc_white_stim";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "#ror_itemDesc_white_tesla_trap";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "#ror_itemDesc_white_electrified_armor";
			}
			else if (name == "asw_weapon_mines")
			{
				return "#ror_itemDesc_white_mines";
			}
			else if (name == "asw_weapon_fist")
			{
				return "#ror_itemDesc_white_fist";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "#ror_itemDesc_white_grenades";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "#ror_itemDesc_white_night_vision";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "#ror_itemDesc_white_smart_bomb";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "#ror_itemDesc_white_gas_grenades";
			}
			else if (name == "asw_weapon_blink")
			{
				return "#ror_itemDesc_white_blink";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "#ror_itemDesc_white_jump_jet";
			}
		}
		if (tier == "tierGreen")
		{
			if (name == "asw_weapon_medkit")
			{
				return "#ror_itemDesc_green_medkit";
			}
			else if (name == "asw_weapon_welder")
			{
				return "#ror_itemDesc_green_welder";
			}
			else if (name == "asw_weapon_flares")
			{
				return "#ror_itemDesc_green_flares";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "#ror_itemDesc_green_laser_mines";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "#ror_itemDesc_green_normal_armor";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "#ror_itemDesc_green_buff_grenade";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "#ror_itemDesc_green_hornet_barrage";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "#ror_itemDesc_green_freeze_grenades";
			}
			else if (name == "asw_weapon_stim")
			{
				return "#ror_itemDesc_green_stim";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "#ror_itemDesc_green_tesla_trap";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "#ror_itemDesc_green_electrified_armor";
			}
			else if (name == "asw_weapon_mines")
			{
				return "#ror_itemDesc_green_mines";
			}
			else if (name == "asw_weapon_fist")
			{
				return "#ror_itemDesc_green_fist";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "#ror_itemDesc_green_grenades";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "#ror_itemDesc_green_night_vision";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "#ror_itemDesc_green_smart_bomb";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "#ror_itemDesc_green_gas_grenades";
			}
			else if (name == "asw_weapon_blink")
			{
				return "#ror_itemDesc_green_blink";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "#ror_itemDesc_green_jump_jet";
			}
		}
		if (tier == "tierRed")
		{
			if (name == "asw_weapon_medkit")
			{
				return "#ror_itemDesc_red_medkit";
			}
			else if (name == "asw_weapon_welder")
			{
				return "#ror_itemDesc_red_welder";
			}
			else if (name == "asw_weapon_flares")
			{
				return "#ror_itemDesc_red_flares";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "#ror_itemDesc_red_laser_mines";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "#ror_itemDesc_red_normal_armor";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "#ror_itemDesc_red_buff_grenade";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "#ror_itemDesc_red_hornet_barrage";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "#ror_itemDesc_red_freeze_grenades";
			}
			else if (name == "asw_weapon_stim")
			{
				return "#ror_itemDesc_red_stim";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "#ror_itemDesc_red_tesla_trap";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "#ror_itemDesc_red_electrified_armor";
			}
			else if (name == "asw_weapon_mines")
			{
				return "#ror_itemDesc_red_mines";
			}
			else if (name == "asw_weapon_fist")
			{
				return "#ror_itemDesc_red_fist";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "#ror_itemDesc_red_grenades";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "#ror_itemDesc_red_night_vision";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "#ror_itemDesc_red_smart_bomb";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "#ror_itemDesc_red_gas_grenades";
			}
			else if (name == "asw_weapon_blink")
			{
				return "#ror_itemDesc_red_blink";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "#ror_itemDesc_red_jump_jet";
			}
		}
	}
	else
	{
		if (tier == "tierWhite")
		{
			if (name == "asw_weapon_medkit")
			{
				return "Chance to drop Healing Kit on kill. Stacks chance.";
			}
			else if (name == "asw_weapon_welder")
			{
				return "All nearby teammates gain +50% damage and +50% resistance during hack. Stacks range.";
			}
			else if (name == "asw_weapon_flares")
			{
				return "Chance to ignite enemy on hit. Stacks chance.";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "Explode taken damage once. Recharges when out of danger. Stacks range.";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "+10% resistance. Stacks said effect.";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "Increased damage against enemies above 90% health. Stacks damage.";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "Using a button for the first time fires rockets. Stacks number of rockets.";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "Chance to freeze enemy on hit. Stacks chance.";
			}
			else if (name == "asw_weapon_stim")
			{
				return "Upon taking damage, heal 2 seconds after not being hit. Stacks heal amount.";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "Chance to stun enemy on hit. Stacks chance.";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "Resist damage once. Recharges when out of danger. Stacks resistance.";
			}
			else if (name == "asw_weapon_mines")
			{
				return "Killed enemies ignite nearby enemies. Stacks range.";
			}
			else if (name == "asw_weapon_fist")
			{
				return "Increased damage in close range. Stacks damage.";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "Chance to spawn a grenade on hit. Stacks chance.";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "+10% CRIT chance. Stacks said effect.";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "On stage start, equipments start with extra charges. Stacks number of extra charges.";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "Heal when out of danger. Stacks heal amount.";
			}
			else if (name == "asw_weapon_blink")
			{
				return "Upon being hit, chance to nullify taken damage. Stacks chance.";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "Increased damage against large aliens. Stacks damage.";
			}
		}
		if (tier == "tierGreen")
		{
			if (name == "asw_weapon_medkit")
			{
				return "+5% CRIT chance. Heal on critial hit. Stacks heal amount.";
			}
			else if (name == "asw_weapon_welder")
			{
				return "Chance to drop bullet sentry on kill. Stacks chance.";
			}
			else if (name == "asw_weapon_flares")
			{
				return "Increased damage against burning enemies. Stacks damage.";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "Instantly kill elite enemies below a certain amount of health. Stacks threshold.";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "Killing 4 enemies in 1 second provides +50% resistance and +80% damage. Stacks effect duration.";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "+5% CRIT chance. Critical hits inherit 80% of the damage to target's nearby enemies. Stacks number of enemies that can be affected.";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "10% chance to fire a rocket on hit. Stacks rocket damage.";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "When infested, freeze all nearby enemies. Stacks range.";
			}
			else if (name == "asw_weapon_stim")
			{
				return "50% chance to heal on hit. Stacks heal amount.";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "10% chance on hit to stun all enemies near target. Stacks range.";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "Deal damage to number of nearby enemies upon being hit. Stacks range and number of enemies that can be affected.";
			}
			else if (name == "asw_weapon_mines")
			{
				return "Falling below 25% health spawns a high damage incendiary grenade. Stacks cooldown reduction.";
			}
			else if (name == "asw_weapon_fist")
			{
				return "Drain number of nearby enemies' health. Stacks number of enemies that can be affected.";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "Killed enemies explode. Stacks range and damage.";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "Dealing damage to enemies that are both stunned and burning marks them for death. Marked enemies receive +50% TOTAL damage. Stacks effect duration.";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "Chance to drop ammo on kill. Stacks chance.";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "Heal all nearby teammates during hack. Stacks heal amount.";
			}
			else if (name == "asw_weapon_blink")
			{
				return "Falling below 25% health freezes all nearby enemies. Stacks range and cooldown reduction.";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "Increased damage when out of danger. Stacks damage.";
			}
		}
		if (tier == "tierRed")
		{
			if (name == "asw_weapon_medkit")
			{
				return "Falling below 25% health heals 75% of maximum health. Stacks cooldown reduction.";
			}
			else if (name == "asw_weapon_welder")
			{
				return "Chance on hit to hook target's nearby enemies. Stacks chance and number of enemies that can be affected.";
			}
			else if (name == "asw_weapon_flares")
			{
				return "Ignite and stun all nearby enemies during hack. Stacks range.";
			}
			else if (name == "asw_weapon_laser_mines")
			{
				return "Chance to cause large damage within a small area on hit. Stacks damage.";
			}
			else if (name == "asw_weapon_normal_armor")
			{
				return "Elite kills grant +80% resistance and +100% CRIT chance. Stacks effect duration.";
			}
			else if (name == "asw_weapon_buff_grenade")
			{
				return "Increase critical damage by 100%. Stacks said effect.";
			}
			else if (name == "asw_weapon_hornet_barrage")
			{
				return "Kills spawn rockets. Stacks rocket damage.";
			}
			else if (name == "asw_weapon_freeze_grenades")
			{
				return "Nearby frozen enemies take damage. Stacks range.";
			}
			else if (name == "asw_weapon_stim")
			{
				return "Chance to drop a healing beacon on kill. Stacks beacon range.";
			}
			else if (name == "asw_weapon_tesla_trap")
			{
				return "Stun nearby enemies every second. Stacks number of enemies that can be affected.";
			}
			else if (name == "asw_weapon_electrified_armor")
			{
				return "Taking damage activates Electric Armor for 5 seconds. Stacks cooldown reduction.";
			}
			else if (name == "asw_weapon_mines")
			{
				return "+5% CRIT chance. Burn enemies on critical hit. Burning enemies explode on death. Stacks damage.";
			}
			else if (name == "asw_weapon_fist")
			{
				return "All hits explode, dealing 60% TOTAL damage. Stacks range.";
			}
			else if (name == "asw_weapon_grenades")
			{
				return "Killing 4 enemies in 7 seconds fires a high damage grenade at the nearest enemy. Stacks damage.";
			}
			else if (name == "asw_weapon_night_vision")
			{
				return "Chance to reanimate killed enemies into ghost allies. Stacks ghost lifespan.";
			}
			else if (name == "asw_weapon_smart_bomb")
			{
				return "All item-spawned rockets spawn 2 more times. Stacks bonus damage to rockets.";
			}
			else if (name == "asw_weapon_gas_grenades")
			{
				return "Heal every second. Infestations are instantly cured when below 50% health. Stacks heal amount.";
			}
			else if (name == "asw_weapon_blink")
			{
				return "All random effects are rolled again for a favorable outcome. Stacks number of rerolls.";
			}
			else if (name == "asw_weapon_jump_jet")
			{
				return "Hitting an enemy permanently increases the damage the enemy takes next time. Stacks increased damage.";
			}
		}
	}
	return name;
}